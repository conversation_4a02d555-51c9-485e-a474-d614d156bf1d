using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace MqttBroker.Host;

/// <summary>
/// MQTT Broker 主机服务，负责管理 MQTT 服务器的生命周期
/// </summary>
public class MqttBrokerService : BackgroundService
{
    private readonly ILogger<MqttBrokerService> _logger;

    public MqttBrokerService(ILogger<MqttBrokerService> logger)
    {
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("MQTT Broker 服务正在启动...");

        try
        {
            // TODO: 启动 MQTT 服务器
            // 这里将在后续开发中实现具体的服务器启动逻辑
            
            while (!stoppingToken.IsCancellationRequested)
            {
                // 保持服务运行
                await Task.Delay(1000, stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("MQTT Broker 服务正在停止...");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MQTT Broker 服务运行时发生错误");
            throw;
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("MQTT Broker 服务正在关闭...");
        
        // TODO: 优雅关闭 MQTT 服务器
        // 这里将在后续开发中实现具体的服务器关闭逻辑
        
        await base.StopAsync(cancellationToken);
        _logger.LogInformation("MQTT Broker 服务已关闭");
    }
}
