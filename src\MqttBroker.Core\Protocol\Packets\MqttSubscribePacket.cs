using System;
using System.Collections.Generic;
using System.Linq;

namespace MqttBroker.Core.Protocol.Packets
{
    /// <summary>
    /// MQTT SUBSCRIBE 数据包
    /// </summary>
    public class MqttSubscribePacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.Subscribe;

        /// <summary>
        /// 数据包标志位 (SUBSCRIBE 固定为 0x02)
        /// </summary>
        public override byte Flags => 0x02;

        /// <summary>
        /// 订阅列表
        /// </summary>
        public List<MqttSubscription> Subscriptions { get; set; } = new();

        /// <summary>
        /// MQTT 5.0 属性（仅适用于 MQTT 5.0）
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            int size = 1; // 固定头部第一个字节
            size += 2; // 数据包标识符

            // MQTT 5.0 属性
            if (Properties != null)
            {
                size += Properties.GetEstimatedSize();
            }

            // 有效载荷 - 订阅列表
            foreach (var subscription in Subscriptions)
            {
                size += GetStringLength(subscription.TopicFilter); // 主题过滤器
                size += 1; // 订阅选项
            }

            // 剩余长度字段
            size += GetVariableByteIntegerLength(size - 1);

            return size;
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // SUBSCRIBE 必须有数据包标识符
            if (!IsPacketIdentifierValid())
                return false;

            // 必须至少有一个订阅
            if (Subscriptions == null || Subscriptions.Count == 0)
                return false;

            // 验证所有订阅
            return Subscriptions.All(s => s.IsValid());
        }

        /// <summary>
        /// 创建 SUBSCRIBE 数据包
        /// </summary>
        /// <param name="packetIdentifier">数据包标识符</param>
        /// <param name="subscriptions">订阅列表</param>
        /// <returns>SUBSCRIBE 数据包</returns>
        public static MqttSubscribePacket Create(ushort packetIdentifier, params MqttSubscription[] subscriptions)
        {
            return new MqttSubscribePacket
            {
                PacketIdentifier = packetIdentifier,
                Subscriptions = subscriptions.ToList()
            };
        }

        /// <summary>
        /// 添加订阅
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        /// <param name="qosLevel">QoS 级别</param>
        /// <param name="options">订阅选项</param>
        public void AddSubscription(string topicFilter, MqttQoSLevel qosLevel, MqttSubscriptionOptions? options = null)
        {
            Subscriptions.Add(new MqttSubscription
            {
                TopicFilter = topicFilter,
                QoSLevel = qosLevel,
                Options = options ?? new MqttSubscriptionOptions()
            });
        }
    }

    /// <summary>
    /// MQTT SUBACK 数据包
    /// </summary>
    public class MqttSubAckPacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.SubAck;

        /// <summary>
        /// 返回码列表 (MQTT 3.1.1) 或原因码列表 (MQTT 5.0)
        /// </summary>
        public List<MqttReasonCode> ReasonCodes { get; set; } = new();

        /// <summary>
        /// MQTT 5.0 属性（仅适用于 MQTT 5.0）
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            int size = 1; // 固定头部第一个字节
            size += 2; // 数据包标识符

            // MQTT 5.0 属性
            if (Properties != null)
            {
                size += Properties.GetEstimatedSize();
            }

            // 有效载荷 - 返回码/原因码列表
            size += ReasonCodes.Count;

            // 剩余长度字段
            size += GetVariableByteIntegerLength(size - 1);

            return size;
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // SUBACK 必须有数据包标识符
            if (!IsPacketIdentifierValid())
                return false;

            // 必须至少有一个返回码
            return ReasonCodes != null && ReasonCodes.Count > 0;
        }

        /// <summary>
        /// 创建 SUBACK 数据包
        /// </summary>
        /// <param name="packetIdentifier">数据包标识符</param>
        /// <param name="reasonCodes">原因码列表</param>
        /// <returns>SUBACK 数据包</returns>
        public static MqttSubAckPacket Create(ushort packetIdentifier, params MqttReasonCode[] reasonCodes)
        {
            return new MqttSubAckPacket
            {
                PacketIdentifier = packetIdentifier,
                ReasonCodes = reasonCodes.ToList()
            };
        }

        /// <summary>
        /// 创建成功的 SUBACK 数据包
        /// </summary>
        /// <param name="packetIdentifier">数据包标识符</param>
        /// <param name="grantedQoSLevels">授权的 QoS 级别列表</param>
        /// <returns>SUBACK 数据包</returns>
        public static MqttSubAckPacket CreateSuccess(ushort packetIdentifier, params MqttQoSLevel[] grantedQoSLevels)
        {
            var reasonCodes = grantedQoSLevels.Select(qos => qos switch
            {
                MqttQoSLevel.AtMostOnce => MqttReasonCode.GrantedQoS0,
                MqttQoSLevel.AtLeastOnce => MqttReasonCode.GrantedQoS1,
                MqttQoSLevel.ExactlyOnce => MqttReasonCode.GrantedQoS2,
                _ => MqttReasonCode.UnspecifiedError
            }).ToArray();

            return Create(packetIdentifier, reasonCodes);
        }
    }

    /// <summary>
    /// MQTT 订阅信息
    /// </summary>
    public class MqttSubscription
    {
        /// <summary>
        /// 主题过滤器
        /// </summary>
        public string TopicFilter { get; set; } = string.Empty;

        /// <summary>
        /// QoS 级别
        /// </summary>
        public MqttQoSLevel QoSLevel { get; set; }

        /// <summary>
        /// 订阅选项 (MQTT 5.0)
        /// </summary>
        public MqttSubscriptionOptions Options { get; set; } = new();

        /// <summary>
        /// 验证订阅的有效性
        /// </summary>
        /// <returns>如果订阅有效则返回 true，否则返回 false</returns>
        public bool IsValid()
        {
            // 验证主题过滤器
            if (!MqttPacketBase.IsTopicFilterValid(TopicFilter))
                return false;

            // 验证QoS级别
            if (!MqttPacketBase.IsQoSLevelValid(QoSLevel))
                return false;

            return true;
        }

        /// <summary>
        /// 获取订阅选项字节
        /// </summary>
        /// <returns>订阅选项字节</returns>
        public byte GetOptionsAsByte()
        {
            byte options = (byte)QoSLevel;

            if (Options.NoLocal)
                options |= 0x04;

            if (Options.RetainAsPublished)
                options |= 0x08;

            options |= (byte)((byte)Options.RetainHandling << 4);

            return options;
        }

        /// <summary>
        /// 从字节解析订阅选项
        /// </summary>
        /// <param name="optionsByte">选项字节</param>
        public void ParseOptionsFromByte(byte optionsByte)
        {
            QoSLevel = (MqttQoSLevel)(optionsByte & 0x03);
            Options.NoLocal = (optionsByte & 0x04) != 0;
            Options.RetainAsPublished = (optionsByte & 0x08) != 0;
            Options.RetainHandling = (MqttRetainHandling)((optionsByte & 0x30) >> 4);
        }
    }

    /// <summary>
    /// MQTT 5.0 订阅选项
    /// </summary>
    public class MqttSubscriptionOptions
    {
        /// <summary>
        /// 不转发本地消息
        /// </summary>
        public bool NoLocal { get; set; }

        /// <summary>
        /// 保持发布时的保留标志
        /// </summary>
        public bool RetainAsPublished { get; set; }

        /// <summary>
        /// 保留消息处理方式
        /// </summary>
        public MqttRetainHandling RetainHandling { get; set; } = MqttRetainHandling.SendAtSubscribe;
    }

    /// <summary>
    /// MQTT 5.0 保留消息处理方式
    /// </summary>
    public enum MqttRetainHandling : byte
    {
        /// <summary>
        /// 订阅时发送保留消息
        /// </summary>
        SendAtSubscribe = 0,

        /// <summary>
        /// 仅在新订阅时发送保留消息
        /// </summary>
        SendAtSubscribeIfNew = 1,

        /// <summary>
        /// 不发送保留消息
        /// </summary>
        DoNotSend = 2
    }
}
