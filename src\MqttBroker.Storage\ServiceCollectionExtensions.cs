using Microsoft.Extensions.DependencyInjection;

namespace MqttBroker.Storage;

/// <summary>
/// MQTT Broker 存储服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 MQTT Broker 存储服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttBrokerStorage(this IServiceCollection services)
    {
        // TODO: 注册存储服务
        // 这里将在后续开发中添加具体的服务注册
        
        return services;
    }
}
