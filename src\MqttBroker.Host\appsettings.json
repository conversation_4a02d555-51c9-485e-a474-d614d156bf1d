{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "MqttBroker": {"Network": {"Tcp": {"Enabled": true, "Port": 1883, "Address": "0.0.0.0", "Backlog": 100, "NoDelay": true}, "Tls": {"Enabled": false, "Port": 8883, "Address": "0.0.0.0", "CertificatePath": null, "CertificatePassword": null, "RequireClientCertificate": false, "TlsProtocols": "Tls12,Tls13"}, "WebSocket": {"Enabled": false, "Port": 8080, "SecurePort": 8443, "Path": "/mqtt", "SubProtocols": ["mqtt", "mqttv3.1", "mqttv3.1.1", "mqttv5.0"]}, "Connection": {"MaxConnections": 10000, "ConnectionTimeout": 30, "KeepAliveTimeout": 60, "ReceiveBufferSize": 8192, "SendBufferSize": 8192, "EnableRateLimit": true, "MaxConnectionsPerSecond": 100}, "Performance": {"UseMemoryPool": true, "MaxPooledBufferSize": 65536, "EnableZeroCopy": true, "WorkerThreads": 0, "IOThreads": 0, "EnableBackpressure": true, "BackpressureThreshold": 1048576, "BatchSize": 100}}, "Storage": {"ConnectionString": "Data Source=data/mqtt_broker.db", "EnablePersistence": true}, "Security": {"AllowAnonymous": true, "RequireAuthentication": false}, "Performance": {"MaxMessageSize": 268435456, "MaxTopicLength": 65535, "MaxClientIdLength": 23}}}