using Microsoft.Extensions.DependencyInjection;
using MqttBroker.Core.Protocol;

namespace MqttBroker.Core;

/// <summary>
/// MQTT Broker 核心服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 MQTT Broker 核心服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttBrokerCore(this IServiceCollection services)
    {
        // 注册 MQTT 协议解析器相关服务
        services.AddSingleton<IMqttPacketParser, MqttPacketParser>();
        services.AddSingleton<IMqttPacketSerializer, MqttPacketSerializer>();
        services.AddSingleton<IMqttPacketValidator, MqttPacketValidator>();
        services.AddSingleton<IMqttTopicMatcher, MqttTopicMatcher>();

        // TODO: 注册其他核心服务
        // 例如：services.AddSingleton<IClientManager, ClientManager>();

        return services;
    }
}
