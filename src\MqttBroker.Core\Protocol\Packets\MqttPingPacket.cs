using System;

namespace MqttBroker.Core.Protocol.Packets
{
    /// <summary>
    /// MQTT PINGREQ 数据包 (心跳请求)
    /// </summary>
    public class MqttPingReqPacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.PingReq;

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            // PINGREQ 数据包只有固定头部，剩余长度为 0
            return 2; // 1字节数据包类型 + 1字节剩余长度(0)
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // PINGREQ 不应该有数据包标识符
            if (PacketIdentifier.HasValue)
                return false;

            // PINGREQ 标志位必须为 0
            if (Flags != 0)
                return false;

            return true;
        }

        /// <summary>
        /// 创建 PINGREQ 数据包
        /// </summary>
        /// <returns>PINGREQ 数据包</returns>
        public static MqttPingReqPacket Create()
        {
            return new MqttPingReqPacket();
        }
    }

    /// <summary>
    /// MQTT PINGRESP 数据包 (心跳响应)
    /// </summary>
    public class MqttPingRespPacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.PingResp;

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            // PINGRESP 数据包只有固定头部，剩余长度为 0
            return 2; // 1字节数据包类型 + 1字节剩余长度(0)
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // PINGRESP 不应该有数据包标识符
            if (PacketIdentifier.HasValue)
                return false;

            // PINGRESP 标志位必须为 0
            if (Flags != 0)
                return false;

            return true;
        }

        /// <summary>
        /// 创建 PINGRESP 数据包
        /// </summary>
        /// <returns>PINGRESP 数据包</returns>
        public static MqttPingRespPacket Create()
        {
            return new MqttPingRespPacket();
        }
    }
}
