using System;
using System.Buffers;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Protocol
{
    /// <summary>
    /// MQTT 数据包解析器实现
    /// </summary>
    public class MqttPacketParser : IMqttPacketParser
    {
        private readonly ILogger<MqttPacketParser> _logger;

        /// <summary>
        /// 初始化 MQTT 数据包解析器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public MqttPacketParser(ILogger<MqttPacketParser> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 解析 MQTT 数据包
        /// </summary>
        /// <param name="buffer">包含数据包数据的缓冲区</param>
        /// <param name="protocolVersion">MQTT 协议版本</param>
        /// <returns>解析后的数据包，如果解析失败则返回 null</returns>
        public IMqttPacket? ParsePacket(ReadOnlySequence<byte> buffer, MqttProtocolVersion protocolVersion)
        {
            try
            {
                if (!TryParsePacketHeader(buffer, out var packetLength, out var headerLength))
                {
                    _logger.LogDebug("Failed to parse packet header");
                    return null;
                }

                if (buffer.Length < packetLength)
                {
                    _logger.LogDebug("Buffer does not contain complete packet. Expected: {ExpectedLength}, Actual: {ActualLength}", 
                        packetLength, buffer.Length);
                    return null;
                }

                var reader = new MqttBinaryReader(buffer);
                
                // 读取固定头部
                var firstByte = reader.ReadByte();
                var packetType = (MqttPacketType)((firstByte & MqttProtocolConstants.PacketTypeMasks.PacketType) >> 4);
                var flags = (byte)(firstByte & MqttProtocolConstants.PacketTypeMasks.Flags);
                
                // 读取剩余长度
                var remainingLength = reader.ReadVariableByteInteger();

                _logger.LogDebug("Parsing packet: Type={PacketType}, Flags={Flags:X2}, RemainingLength={RemainingLength}", 
                    packetType, flags, remainingLength);

                // 根据数据包类型解析
                return packetType switch
                {
                    MqttPacketType.Connect => ParseConnectPacket(reader, flags, protocolVersion),
                    MqttPacketType.ConnAck => ParseConnAckPacket(reader, flags, protocolVersion),
                    MqttPacketType.Publish => ParsePublishPacket(reader, flags, protocolVersion),
                    MqttPacketType.PubAck => ParsePubAckPacket(reader, flags, protocolVersion),
                    MqttPacketType.PubRec => ParsePubRecPacket(reader, flags, protocolVersion),
                    MqttPacketType.PubRel => ParsePubRelPacket(reader, flags, protocolVersion),
                    MqttPacketType.PubComp => ParsePubCompPacket(reader, flags, protocolVersion),
                    MqttPacketType.Subscribe => ParseSubscribePacket(reader, flags, protocolVersion),
                    MqttPacketType.SubAck => ParseSubAckPacket(reader, flags, protocolVersion),
                    MqttPacketType.Unsubscribe => ParseUnsubscribePacket(reader, flags, protocolVersion),
                    MqttPacketType.UnsubAck => ParseUnsubAckPacket(reader, flags, protocolVersion),
                    MqttPacketType.PingReq => ParsePingReqPacket(reader, flags),
                    MqttPacketType.PingResp => ParsePingRespPacket(reader, flags),
                    MqttPacketType.Disconnect => ParseDisconnectPacket(reader, flags, protocolVersion),
                    MqttPacketType.Auth => ParseAuthPacket(reader, flags, protocolVersion),
                    _ => null // 返回 null 而不是抛出异常
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing MQTT packet");
                return null;
            }
        }

        /// <summary>
        /// 尝试解析数据包头部以确定数据包长度
        /// </summary>
        /// <param name="buffer">包含数据包头部的缓冲区</param>
        /// <param name="packetLength">输出参数：数据包总长度</param>
        /// <param name="headerLength">输出参数：头部长度</param>
        /// <returns>如果成功解析头部则返回 true，否则返回 false</returns>
        public bool TryParsePacketHeader(ReadOnlySequence<byte> buffer, out int packetLength, out int headerLength)
        {
            packetLength = 0;
            headerLength = 0;

            if (buffer.Length < 2) // 至少需要 2 字节（类型 + 最小剩余长度）
                return false;

            try
            {
                var reader = new MqttBinaryReader(buffer);
                
                // 跳过第一个字节（数据包类型和标志）
                reader.ReadByte();
                headerLength = 1;

                // 读取剩余长度
                int remainingLength = 0;
                int multiplier = 1;
                int byteCount = 0;

                while (true)
                {
                    if (byteCount >= 4 || !reader.HasBytes(1))
                        return false;

                    var encodedByte = reader.ReadByte();
                    headerLength++;
                    
                    remainingLength += (encodedByte & 0x7F) * multiplier;

                    if ((encodedByte & 0x80) == 0)
                        break;

                    multiplier *= 128;
                    byteCount++;
                }

                packetLength = headerLength + remainingLength;
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查缓冲区是否包含完整的数据包
        /// </summary>
        /// <param name="buffer">要检查的缓冲区</param>
        /// <returns>如果包含完整数据包则返回 true，否则返回 false</returns>
        public bool HasCompletePacket(ReadOnlySequence<byte> buffer)
        {
            if (!TryParsePacketHeader(buffer, out var packetLength, out _))
                return false;

            return buffer.Length >= packetLength;
        }

        #region 私有解析方法

        private MqttConnectPacket ParseConnectPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttConnectPacket();

            // 解析可变头部
            packet.ProtocolName = reader.ReadString();
            packet.ProtocolVersion = (MqttProtocolVersion)reader.ReadByte();
            var connectFlags = reader.ReadByte();
            packet.KeepAlive = reader.ReadUInt16();

            // 解析连接标志
            packet.CleanSession = (connectFlags & MqttProtocolConstants.ConnectFlags.CleanSessionFlag) != 0;
            var hasWill = (connectFlags & MqttProtocolConstants.ConnectFlags.WillFlag) != 0;
            var hasUsername = (connectFlags & MqttProtocolConstants.ConnectFlags.UsernameFlag) != 0;
            var hasPassword = (connectFlags & MqttProtocolConstants.ConnectFlags.PasswordFlag) != 0;

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                packet.Properties = ParseProperties(reader);
            }

            // 解析有效载荷
            packet.ClientId = reader.ReadString();

            if (hasWill)
            {
                packet.WillMessage = new MqttWillMessage();
                
                // MQTT 5.0 遗嘱属性
                if (protocolVersion == MqttProtocolVersion.Version50)
                {
                    packet.WillMessage.Properties = ParseProperties(reader);
                }

                packet.WillMessage.Topic = reader.ReadString();
                packet.WillMessage.Payload = reader.ReadBinaryData();
                packet.WillMessage.QoSLevel = (MqttQoSLevel)((connectFlags & MqttProtocolConstants.ConnectFlags.WillQoSMask) >> 3);
                packet.WillMessage.Retain = (connectFlags & MqttProtocolConstants.ConnectFlags.WillRetainFlag) != 0;
            }

            if (hasUsername)
            {
                packet.Username = reader.ReadString();
            }

            if (hasPassword)
            {
                packet.Password = reader.ReadString();
            }

            return packet;
        }

        private MqttConnAckPacket ParseConnAckPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttConnAckPacket();

            var acknowledgeFlags = reader.ReadByte();
            packet.SessionPresent = (acknowledgeFlags & 0x01) != 0;

            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(reader);
                }
            }
            else
            {
                packet.ReturnCode = (MqttConnectReturnCode)reader.ReadByte();
            }

            return packet;
        }

        private MqttPublishPacket ParsePublishPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttPublishPacket();
            packet.ParseFlags(flags);

            // 解析可变头部
            packet.Topic = reader.ReadString();

            if (packet.QoSLevel > MqttQoSLevel.AtMostOnce)
            {
                packet.PacketIdentifier = reader.ReadUInt16();
            }

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                packet.Properties = ParseProperties(reader);
            }

            // 解析有效载荷
            packet.Payload = reader.ReadRemainingBytes();

            return packet;
        }

        private MqttPubAckPacket ParsePubAckPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttPubAckPacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            if (protocolVersion == MqttProtocolVersion.Version50 && reader.Remaining > 0)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(reader);
                }
            }

            return packet;
        }

        private MqttPubRecPacket ParsePubRecPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttPubRecPacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            if (protocolVersion == MqttProtocolVersion.Version50 && reader.Remaining > 0)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(reader);
                }
            }

            return packet;
        }

        private MqttPubRelPacket ParsePubRelPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttPubRelPacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            if (protocolVersion == MqttProtocolVersion.Version50 && reader.Remaining > 0)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(reader);
                }
            }

            return packet;
        }

        private MqttPubCompPacket ParsePubCompPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttPubCompPacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            if (protocolVersion == MqttProtocolVersion.Version50 && reader.Remaining > 0)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(reader);
                }
            }

            return packet;
        }

        private MqttPingReqPacket ParsePingReqPacket(MqttBinaryReader reader, byte flags)
        {
            return new MqttPingReqPacket();
        }

        private MqttPingRespPacket ParsePingRespPacket(MqttBinaryReader reader, byte flags)
        {
            return new MqttPingRespPacket();
        }

        private MqttProperties? ParseProperties(MqttBinaryReader reader)
        {
            var propertiesLength = reader.ReadVariableByteInteger();
            if (propertiesLength == 0)
                return null;

            var properties = new MqttProperties();
            var endPosition = reader.Consumed + propertiesLength;

            while (reader.Consumed < endPosition)
            {
                var propertyId = (MqttPropertyType)reader.ReadByte();
                
                // 根据属性类型读取值
                object value = propertyId switch
                {
                    MqttPropertyType.PayloadFormatIndicator => reader.ReadByte(),
                    MqttPropertyType.MessageExpiryInterval => reader.ReadUInt32(),
                    MqttPropertyType.ContentType => reader.ReadString(),
                    MqttPropertyType.ResponseTopic => reader.ReadString(),
                    MqttPropertyType.CorrelationData => reader.ReadBinaryData(),
                    MqttPropertyType.SubscriptionIdentifier => reader.ReadVariableByteInteger(),
                    MqttPropertyType.SessionExpiryInterval => reader.ReadUInt32(),
                    MqttPropertyType.AssignedClientIdentifier => reader.ReadString(),
                    MqttPropertyType.ServerKeepAlive => reader.ReadUInt16(),
                    MqttPropertyType.AuthenticationMethod => reader.ReadString(),
                    MqttPropertyType.AuthenticationData => reader.ReadBinaryData(),
                    MqttPropertyType.RequestProblemInformation => reader.ReadByte(),
                    MqttPropertyType.WillDelayInterval => reader.ReadUInt32(),
                    MqttPropertyType.RequestResponseInformation => reader.ReadByte(),
                    MqttPropertyType.ResponseInformation => reader.ReadString(),
                    MqttPropertyType.ServerReference => reader.ReadString(),
                    MqttPropertyType.ReasonString => reader.ReadString(),
                    MqttPropertyType.ReceiveMaximum => reader.ReadUInt16(),
                    MqttPropertyType.TopicAliasMaximum => reader.ReadUInt16(),
                    MqttPropertyType.TopicAlias => reader.ReadUInt16(),
                    MqttPropertyType.MaximumQoS => reader.ReadByte(),
                    MqttPropertyType.RetainAvailable => reader.ReadByte(),
                    MqttPropertyType.UserProperty => (reader.ReadString(), reader.ReadString()),
                    MqttPropertyType.MaximumPacketSize => reader.ReadUInt32(),
                    MqttPropertyType.WildcardSubscriptionAvailable => reader.ReadByte(),
                    MqttPropertyType.SubscriptionIdentifierAvailable => reader.ReadByte(),
                    MqttPropertyType.SharedSubscriptionAvailable => reader.ReadByte(),
                    _ => throw new NotSupportedException($"Property type {propertyId} is not supported")
                };

                properties.Properties[propertyId] = value;
            }

            return properties;
        }

        private MqttSubscribePacket ParseSubscribePacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttSubscribePacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                packet.Properties = ParseProperties(reader);
            }

            // 解析订阅列表
            while (reader.Remaining > 0)
            {
                var subscription = new MqttSubscription
                {
                    TopicFilter = reader.ReadString()
                };

                var options = reader.ReadByte();
                subscription.ParseOptionsFromByte(options);

                packet.Subscriptions.Add(subscription);
            }

            return packet;
        }

        private MqttSubAckPacket ParseSubAckPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttSubAckPacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                packet.Properties = ParseProperties(reader);
            }

            // 解析返回码列表
            while (reader.Remaining > 0)
            {
                packet.ReasonCodes.Add((MqttReasonCode)reader.ReadByte());
            }

            return packet;
        }

        private MqttUnsubscribePacket ParseUnsubscribePacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttUnsubscribePacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                packet.Properties = ParseProperties(reader);
            }

            // 解析主题过滤器列表
            while (reader.Remaining > 0)
            {
                packet.TopicFilters.Add(reader.ReadString());
            }

            return packet;
        }

        private MqttUnsubAckPacket ParseUnsubAckPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttUnsubAckPacket
            {
                PacketIdentifier = reader.ReadUInt16()
            };

            // MQTT 5.0 属性和原因码
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(reader);
                }

                // 解析原因码列表
                while (reader.Remaining > 0)
                {
                    packet.ReasonCodes.Add((MqttReasonCode)reader.ReadByte());
                }
            }

            return packet;
        }

        private MqttDisconnectPacket ParseDisconnectPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            var packet = new MqttDisconnectPacket();

            if (protocolVersion == MqttProtocolVersion.Version50 && reader.Remaining > 0)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(reader);
                }
            }

            return packet;
        }

        private MqttAuthPacket ParseAuthPacket(MqttBinaryReader reader, byte flags, MqttProtocolVersion protocolVersion)
        {
            if (protocolVersion != MqttProtocolVersion.Version50)
                throw new NotSupportedException("AUTH packet is only supported in MQTT 5.0");

            var packet = new MqttAuthPacket();

            if (reader.Remaining > 0)
            {
                packet.ReasonCode = (MqttReasonCode)reader.ReadByte();
                if (reader.Remaining > 0)
                {
                    packet.Properties = ParseProperties(reader);
                }
            }

            return packet;
        }

        #endregion
    }
}
