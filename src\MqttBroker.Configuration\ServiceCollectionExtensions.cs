using Microsoft.Extensions.DependencyInjection;

namespace MqttBroker.Configuration;

/// <summary>
/// MQTT Broker 配置服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 MQTT Broker 配置服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttBrokerConfiguration(this IServiceCollection services)
    {
        // TODO: 注册配置服务
        // 这里将在后续开发中添加具体的服务注册
        
        return services;
    }
}
