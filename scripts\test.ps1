# MQTT Broker 测试脚本
# 用法: .\scripts\test.ps1 [Unit|Integration|Performance|All]

param(
    [string]$TestType = "All"
)

Write-Host "正在运行 MQTT Broker 测试..." -ForegroundColor Green
Write-Host "测试类型: $TestType" -ForegroundColor Yellow

# 构建项目
Write-Host "正在构建项目..." -ForegroundColor Yellow
dotnet build MqttBroker.sln

if ($LASTEXITCODE -ne 0) {
    Write-Host "构建失败!" -ForegroundColor Red
    exit 1
}

Write-Host "构建成功!" -ForegroundColor Green

# 运行测试
switch ($TestType.ToLower()) {
    "unit" {
        Write-Host "正在运行单元测试..." -ForegroundColor Yellow
        dotnet test tests/MqttBroker.Tests.Unit --logger "console;verbosity=normal"
    }
    "integration" {
        Write-Host "正在运行集成测试..." -ForegroundColor Yellow
        dotnet test tests/MqttBroker.Tests.Integration --logger "console;verbosity=normal"
    }
    "performance" {
        Write-Host "正在运行性能测试..." -ForegroundColor Yellow
        dotnet test tests/MqttBroker.Tests.Performance --logger "console;verbosity=normal"
    }
    "all" {
        Write-Host "正在运行所有测试..." -ForegroundColor Yellow
        dotnet test --logger "console;verbosity=normal"
    }
    default {
        Write-Host "无效的测试类型: $TestType" -ForegroundColor Red
        Write-Host "有效选项: Unit, Integration, Performance, All" -ForegroundColor Yellow
        exit 1
    }
}

if ($LASTEXITCODE -eq 0) {
    Write-Host "所有测试通过!" -ForegroundColor Green
} else {
    Write-Host "测试失败!" -ForegroundColor Red
    exit 1
}
