using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using MqttBroker.Core;
using MqttBroker.Network.Abstractions;
using MqttBroker.Network.Configuration;

namespace MqttBroker.Network.Examples;

/// <summary>
/// 网络层使用示例
/// </summary>
public class NetworkExample
{
    /// <summary>
    /// 运行网络层示例
    /// </summary>
    /// <param name="args">命令行参数</param>
    /// <returns>运行任务</returns>
    public static async Task RunAsync(string[] args)
    {
        // 创建主机构建器
        var hostBuilder = Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder(args)
            .ConfigureServices((context, services) =>
            {
                // 注册核心服务
                services.AddMqttBrokerCore();
                
                // 注册网络服务
                services.AddMqttBrokerNetwork(context.Configuration);
                
                // 注册示例服务
                services.AddHostedService<NetworkExampleService>();
            });

        // 构建并运行主机
        var host = hostBuilder.Build();
        
        var logger = host.Services.GetRequiredService<ILogger<NetworkExample>>();
        logger.LogInformation("Starting MQTT Broker Network Example");

        try
        {
            await host.RunAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error running network example");
            throw;
        }
    }
}

/// <summary>
/// 网络示例后台服务
/// </summary>
public class NetworkExampleService : BackgroundService
{
    private readonly ILogger<NetworkExampleService> _logger;
    private readonly IConnectionManager _connectionManager;
    private readonly IPacketHandlerManager _packetHandlerManager;
    private readonly INetworkMiddlewareManager _middlewareManager;

    /// <summary>
    /// 初始化网络示例服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="connectionManager">连接管理器</param>
    /// <param name="packetHandlerManager">数据包处理器管理器</param>
    /// <param name="middlewareManager">中间件管理器</param>
    public NetworkExampleService(
        ILogger<NetworkExampleService> logger,
        IConnectionManager connectionManager,
        IPacketHandlerManager packetHandlerManager,
        INetworkMiddlewareManager middlewareManager)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _packetHandlerManager = packetHandlerManager ?? throw new ArgumentNullException(nameof(packetHandlerManager));
        _middlewareManager = middlewareManager ?? throw new ArgumentNullException(nameof(middlewareManager));
    }

    /// <summary>
    /// 执行示例服务
    /// </summary>
    /// <param name="stoppingToken">停止令牌</param>
    /// <returns>执行任务</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Network Example Service started");

        // 订阅连接事件
        _connectionManager.ClientConnected += OnClientConnected;
        _connectionManager.ClientDisconnected += OnClientDisconnected;

        // 启动统计信息输出定时器
        using var timer = new Timer(OutputStatistics, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));

        try
        {
            // 等待停止信号
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            // 正常停止
        }
        finally
        {
            // 取消订阅事件
            _connectionManager.ClientConnected -= OnClientConnected;
            _connectionManager.ClientDisconnected -= OnClientDisconnected;
        }

        _logger.LogInformation("Network Example Service stopped");
    }

    /// <summary>
    /// 处理客户端连接事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnClientConnected(object? sender, ClientConnectedEventArgs e)
    {
        _logger.LogInformation("Client connected: {ConnectionId} from {RemoteEndPoint}", 
            e.Connection.Id, e.Connection.RemoteEndPoint);

        // 订阅连接的数据包接收事件
        e.Connection.PacketReceived += OnPacketReceived;
    }

    /// <summary>
    /// 处理客户端断开连接事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnClientDisconnected(object? sender, ClientDisconnectedEventArgs e)
    {
        _logger.LogInformation("Client disconnected: {ConnectionId}, Reason={Reason}", 
            e.Connection.Id, e.Reason);

        // 取消订阅连接的数据包接收事件
        e.Connection.PacketReceived -= OnPacketReceived;
    }

    /// <summary>
    /// 处理数据包接收事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private async void OnPacketReceived(object? sender, PacketReceivedEventArgs e)
    {
        if (sender is IClientConnection connection)
        {
            try
            {
                // 使用数据包处理器管理器处理数据包
                await _packetHandlerManager.HandlePacketAsync(e.Packet, connection);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling packet: {PacketType} from {ConnectionId}", 
                    e.Packet.PacketType, connection.Id);
            }
        }
    }

    /// <summary>
    /// 输出统计信息
    /// </summary>
    /// <param name="state">状态对象</param>
    private void OutputStatistics(object? state)
    {
        try
        {
            // 输出连接管理器统计信息
            var connectionStats = _connectionManager.GetStatistics();
            _logger.LogInformation("Connection Statistics: Current={Current}, Total={Total}, Rejected={Rejected}, Utilization={Utilization:F2}%",
                connectionStats.CurrentConnections,
                connectionStats.TotalConnections,
                connectionStats.RejectedConnections,
                connectionStats.ConnectionUtilization);

            // 输出数据包处理器统计信息
            var handlerStats = _packetHandlerManager.GetStatistics();
            _logger.LogInformation("Handler Statistics: Handlers={Handlers}, Handled={Handled}, Failed={Failed}, AvgTime={AvgTime:F2}ms",
                handlerStats.RegisteredHandlers,
                handlerStats.TotalPacketsHandled,
                handlerStats.FailedPackets,
                handlerStats.AverageHandlingTime);

            // 输出中间件统计信息
            var middlewareStats = _middlewareManager.GetStatistics();
            _logger.LogInformation("Middleware Statistics: Middlewares={Middlewares}, Executions={Executions}, Failed={Failed}, AvgTime={AvgTime:F2}ms",
                middlewareStats.RegisteredMiddlewares,
                middlewareStats.TotalExecutions,
                middlewareStats.FailedExecutions,
                middlewareStats.AverageExecutionTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error outputting statistics");
        }
    }
}
