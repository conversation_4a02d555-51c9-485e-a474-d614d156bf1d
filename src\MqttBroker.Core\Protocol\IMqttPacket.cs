using System;
using System.Buffers;

namespace MqttBroker.Core.Protocol
{
    /// <summary>
    /// MQTT 数据包接口
    /// </summary>
    public interface IMqttPacket
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        MqttPacketType PacketType { get; }

        /// <summary>
        /// 数据包标志位
        /// </summary>
        byte Flags { get; }

        /// <summary>
        /// 数据包标识符 (如果适用)
        /// </summary>
        ushort? PacketIdentifier { get; }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        int GetEstimatedSize();

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        bool IsValid();
    }

    /// <summary>
    /// MQTT 数据包解析器接口
    /// </summary>
    public interface IMqttPacketParser
    {
        /// <summary>
        /// 解析 MQTT 数据包
        /// </summary>
        /// <param name="buffer">包含数据包数据的缓冲区</param>
        /// <param name="protocolVersion">MQTT 协议版本</param>
        /// <returns>解析后的数据包，如果解析失败则返回 null</returns>
        IMqttPacket? ParsePacket(ReadOnlySequence<byte> buffer, MqttProtocolVersion protocolVersion);

        /// <summary>
        /// 尝试解析数据包头部以确定数据包长度
        /// </summary>
        /// <param name="buffer">包含数据包头部的缓冲区</param>
        /// <param name="packetLength">输出参数：数据包总长度</param>
        /// <param name="headerLength">输出参数：头部长度</param>
        /// <returns>如果成功解析头部则返回 true，否则返回 false</returns>
        bool TryParsePacketHeader(ReadOnlySequence<byte> buffer, out int packetLength, out int headerLength);

        /// <summary>
        /// 检查缓冲区是否包含完整的数据包
        /// </summary>
        /// <param name="buffer">要检查的缓冲区</param>
        /// <returns>如果包含完整数据包则返回 true，否则返回 false</returns>
        bool HasCompletePacket(ReadOnlySequence<byte> buffer);
    }

    /// <summary>
    /// MQTT 数据包序列化器接口
    /// </summary>
    public interface IMqttPacketSerializer
    {
        /// <summary>
        /// 序列化 MQTT 数据包
        /// </summary>
        /// <param name="packet">要序列化的数据包</param>
        /// <param name="protocolVersion">MQTT 协议版本</param>
        /// <returns>序列化后的字节数组</returns>
        byte[] SerializePacket(IMqttPacket packet, MqttProtocolVersion protocolVersion);

        /// <summary>
        /// 序列化 MQTT 数据包到指定的缓冲区
        /// </summary>
        /// <param name="packet">要序列化的数据包</param>
        /// <param name="buffer">目标缓冲区</param>
        /// <param name="protocolVersion">MQTT 协议版本</param>
        /// <returns>写入的字节数</returns>
        int SerializePacket(IMqttPacket packet, Span<byte> buffer, MqttProtocolVersion protocolVersion);

        /// <summary>
        /// 获取序列化数据包所需的缓冲区大小
        /// </summary>
        /// <param name="packet">要序列化的数据包</param>
        /// <param name="protocolVersion">MQTT 协议版本</param>
        /// <returns>所需的缓冲区大小</returns>
        int GetRequiredBufferSize(IMqttPacket packet, MqttProtocolVersion protocolVersion);
    }

    /// <summary>
    /// MQTT 消息处理器接口
    /// </summary>
    public interface IMqttMessageHandler
    {
        /// <summary>
        /// 处理接收到的 MQTT 数据包
        /// </summary>
        /// <param name="packet">接收到的数据包</param>
        /// <param name="clientId">发送数据包的客户端ID</param>
        /// <returns>处理任务</returns>
        Task HandlePacketAsync(IMqttPacket packet, string clientId);

        /// <summary>
        /// 检查处理器是否可以处理指定类型的数据包
        /// </summary>
        /// <param name="packetType">数据包类型</param>
        /// <returns>如果可以处理则返回 true，否则返回 false</returns>
        bool CanHandle(MqttPacketType packetType);
    }

    /// <summary>
    /// MQTT 数据包验证器接口
    /// </summary>
    public interface IMqttPacketValidator
    {
        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <param name="packet">要验证的数据包</param>
        /// <param name="protocolVersion">MQTT 协议版本</param>
        /// <returns>验证结果</returns>
        MqttPacketValidationResult ValidatePacket(IMqttPacket packet, MqttProtocolVersion protocolVersion);
    }

    /// <summary>
    /// MQTT 数据包验证结果
    /// </summary>
    public class MqttPacketValidationResult
    {
        /// <summary>
        /// 验证是否成功
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息（如果验证失败）
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 错误代码（如果验证失败）
        /// </summary>
        public MqttReasonCode? ReasonCode { get; set; }

        /// <summary>
        /// 创建成功的验证结果
        /// </summary>
        /// <returns>成功的验证结果</returns>
        public static MqttPacketValidationResult Success()
        {
            return new MqttPacketValidationResult { IsValid = true };
        }

        /// <summary>
        /// 创建失败的验证结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="reasonCode">原因码</param>
        /// <returns>失败的验证结果</returns>
        public static MqttPacketValidationResult Failure(string errorMessage, MqttReasonCode? reasonCode = null)
        {
            return new MqttPacketValidationResult
            {
                IsValid = false,
                ErrorMessage = errorMessage,
                ReasonCode = reasonCode
            };
        }
    }
}
