using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Network.Abstractions;
using MqttBroker.Network.Configuration;

namespace MqttBroker.Network.Connection;

/// <summary>
/// 连接管理器实现
/// </summary>
public class ConnectionManager : IConnectionManager
{
    private readonly ILogger<ConnectionManager> _logger;
    private readonly NetworkConfiguration _configuration;
    private readonly ConcurrentDictionary<string, IClientConnection> _connections = new();
    private readonly ConcurrentDictionary<string, string> _clientIdToConnectionId = new();
    private readonly object _lockObject = new();
    private readonly ConnectionManagerStatistics _statistics = new();
    private bool _disposed;

    /// <summary>
    /// 初始化连接管理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">网络配置</param>
    public ConnectionManager(
        ILogger<ConnectionManager> logger,
        IOptions<NetworkConfiguration> configuration)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration?.Value ?? throw new ArgumentNullException(nameof(configuration));
        
        _statistics.MaxConnections = _configuration.Connection.MaxConnections;
    }

    /// <summary>
    /// 当前连接数
    /// </summary>
    public int ConnectionCount => _connections.Count;

    /// <summary>
    /// 最大连接数
    /// </summary>
    public int MaxConnections => _configuration.Connection.MaxConnections;

    /// <summary>
    /// 客户端连接事件
    /// </summary>
    public event EventHandler<ClientConnectedEventArgs>? ClientConnected;

    /// <summary>
    /// 客户端断开连接事件
    /// </summary>
    public event EventHandler<ClientDisconnectedEventArgs>? ClientDisconnected;

    /// <summary>
    /// 添加连接
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <returns>如果成功添加则返回 true，否则返回 false</returns>
    public bool AddConnection(IClientConnection connection)
    {
        if (connection == null)
            throw new ArgumentNullException(nameof(connection));

        lock (_lockObject)
        {
            if (!CanAcceptConnection())
            {
                _logger.LogWarning("Cannot accept new connection, limit reached: {CurrentCount}/{MaxCount}", 
                    ConnectionCount, MaxConnections);
                _statistics.RejectedConnections++;
                return false;
            }

            if (_connections.TryAdd(connection.Id, connection))
            {
                // 订阅连接事件
                connection.ConnectionClosed += OnConnectionClosed;

                _statistics.TotalConnections++;
                _statistics.CurrentConnections = ConnectionCount;

                _logger.LogDebug("Added connection: {ConnectionId} from {RemoteEndPoint}", 
                    connection.Id, connection.RemoteEndPoint);

                // 触发连接事件
                ClientConnected?.Invoke(this, new ClientConnectedEventArgs(connection));

                return true;
            }
            else
            {
                _logger.LogWarning("Failed to add connection: {ConnectionId} (already exists)", connection.Id);
                return false;
            }
        }
    }

    /// <summary>
    /// 移除连接
    /// </summary>
    /// <param name="connectionId">连接 ID</param>
    /// <param name="reason">断开连接原因</param>
    /// <returns>如果成功移除则返回 true，否则返回 false</returns>
    public bool RemoveConnection(string connectionId, DisconnectionReason reason = DisconnectionReason.ClientDisconnected)
    {
        if (string.IsNullOrEmpty(connectionId))
            return false;

        lock (_lockObject)
        {
            if (_connections.TryRemove(connectionId, out var connection))
            {
                // 取消订阅连接事件
                connection.ConnectionClosed -= OnConnectionClosed;

                // 移除客户端 ID 映射
                if (!string.IsNullOrEmpty(connection.ClientId))
                {
                    _clientIdToConnectionId.TryRemove(connection.ClientId, out _);
                }

                _statistics.TotalDisconnections++;
                _statistics.CurrentConnections = ConnectionCount;

                _logger.LogDebug("Removed connection: {ConnectionId}, Reason={Reason}", connectionId, reason);

                // 触发断开连接事件
                ClientDisconnected?.Invoke(this, new ClientDisconnectedEventArgs(connection, reason));

                return true;
            }
            else
            {
                _logger.LogWarning("Failed to remove connection: {ConnectionId} (not found)", connectionId);
                return false;
            }
        }
    }

    /// <summary>
    /// 获取连接
    /// </summary>
    /// <param name="connectionId">连接 ID</param>
    /// <returns>客户端连接，如果不存在则返回 null</returns>
    public IClientConnection? GetConnection(string connectionId)
    {
        if (string.IsNullOrEmpty(connectionId))
            return null;

        _connections.TryGetValue(connectionId, out var connection);
        return connection;
    }

    /// <summary>
    /// 根据客户端 ID 获取连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <returns>客户端连接，如果不存在则返回 null</returns>
    public IClientConnection? GetConnectionByClientId(string clientId)
    {
        if (string.IsNullOrEmpty(clientId))
            return null;

        if (_clientIdToConnectionId.TryGetValue(clientId, out var connectionId))
        {
            return GetConnection(connectionId);
        }

        return null;
    }

    /// <summary>
    /// 获取所有连接
    /// </summary>
    /// <returns>所有客户端连接</returns>
    public IEnumerable<IClientConnection> GetAllConnections()
    {
        return _connections.Values.ToList();
    }

    /// <summary>
    /// 检查是否可以接受新连接
    /// </summary>
    /// <returns>如果可以接受新连接则返回 true，否则返回 false</returns>
    public bool CanAcceptConnection()
    {
        return ConnectionCount < MaxConnections;
    }

    /// <summary>
    /// 关闭所有连接
    /// </summary>
    /// <param name="reason">关闭原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>关闭任务</returns>
    public async Task CloseAllConnectionsAsync(DisconnectionReason reason = DisconnectionReason.ServerShutdown, CancellationToken cancellationToken = default)
    {
        var connections = GetAllConnections().ToList();
        
        _logger.LogInformation("Closing {Count} connections, Reason={Reason}", connections.Count, reason);

        var closeTasks = connections.Select(async connection =>
        {
            try
            {
                await connection.CloseAsync(reason, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error closing connection: {ConnectionId}", connection.Id);
            }
        });

        await Task.WhenAll(closeTasks);

        _logger.LogInformation("All connections closed");
    }

    /// <summary>
    /// 清理超时连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    public async Task CleanupTimeoutConnectionsAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        var connectionTimeout = TimeSpan.FromSeconds(_configuration.Connection.ConnectionTimeout);
        var keepAliveTimeout = TimeSpan.FromSeconds(_configuration.Connection.KeepAliveTimeout);
        
        var connectionsToClose = new List<IClientConnection>();

        foreach (var connection in GetAllConnections())
        {
            try
            {
                var timeSinceLastActivity = now - connection.LastActivity;
                var timeoutThreshold = connection.KeepAliveInterval > 0 
                    ? TimeSpan.FromSeconds(connection.KeepAliveInterval * 1.5) // MQTT 保活超时
                    : (connection.IsAuthenticated ? keepAliveTimeout : connectionTimeout);

                if (timeSinceLastActivity > timeoutThreshold)
                {
                    connectionsToClose.Add(connection);
                    _logger.LogDebug("Connection timeout detected: {ConnectionId}, LastActivity={LastActivity}, Timeout={Timeout}", 
                        connection.Id, connection.LastActivity, timeoutThreshold);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking connection timeout: {ConnectionId}", connection.Id);
            }
        }

        if (connectionsToClose.Count > 0)
        {
            _logger.LogInformation("Cleaning up {Count} timeout connections", connectionsToClose.Count);

            var closeTasks = connectionsToClose.Select(async connection =>
            {
                try
                {
                    await connection.CloseAsync(DisconnectionReason.Timeout, cancellationToken);
                    _statistics.TimeoutConnections++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error closing timeout connection: {ConnectionId}", connection.Id);
                }
            });

            await Task.WhenAll(closeTasks);

            _statistics.CleanedUpConnections += connectionsToClose.Count;
            _statistics.LastCleanupTime = now;
        }
    }

    /// <summary>
    /// 获取连接管理器统计信息
    /// </summary>
    /// <returns>连接管理器统计信息</returns>
    public ConnectionManagerStatistics GetStatistics()
    {
        _statistics.CurrentConnections = ConnectionCount;
        
        // 计算连接使用率
        // 已在属性中计算

        // 按状态分组连接
        _statistics.ConnectionsByState.Clear();
        _statistics.ConnectionsByProtocolVersion.Clear();

        foreach (var connection in GetAllConnections())
        {
            // 按状态分组
            if (_statistics.ConnectionsByState.ContainsKey(connection.State))
            {
                _statistics.ConnectionsByState[connection.State]++;
            }
            else
            {
                _statistics.ConnectionsByState[connection.State] = 1;
            }

            // 按协议版本分组
            var protocolVersion = connection.ProtocolVersion?.ToString() ?? "Unknown";
            if (_statistics.ConnectionsByProtocolVersion.ContainsKey(protocolVersion))
            {
                _statistics.ConnectionsByProtocolVersion[protocolVersion]++;
            }
            else
            {
                _statistics.ConnectionsByProtocolVersion[protocolVersion] = 1;
            }
        }

        return _statistics;
    }

    /// <summary>
    /// 注册客户端 ID 映射
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <param name="connectionId">连接 ID</param>
    public void RegisterClientId(string clientId, string connectionId)
    {
        if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(connectionId))
            return;

        _clientIdToConnectionId.AddOrUpdate(clientId, connectionId, (key, oldValue) => connectionId);
        
        _logger.LogDebug("Registered client ID mapping: {ClientId} -> {ConnectionId}", clientId, connectionId);
    }

    /// <summary>
    /// 处理连接关闭事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnConnectionClosed(object? sender, ConnectionClosedEventArgs e)
    {
        if (sender is IClientConnection connection)
        {
            RemoveConnection(connection.Id, e.Reason);
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            CloseAllConnectionsAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during connection manager disposal");
        }

        _connections.Clear();
        _clientIdToConnectionId.Clear();

        GC.SuppressFinalize(this);
    }
}
