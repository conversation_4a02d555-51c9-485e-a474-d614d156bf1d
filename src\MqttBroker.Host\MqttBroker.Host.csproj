﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\MqttBroker.Core\MqttBroker.Core.csproj" />
    <ProjectReference Include="..\MqttBroker.Network\MqttBroker.Network.csproj" />
    <ProjectReference Include="..\MqttBroker.Storage\MqttBroker.Storage.csproj" />
    <ProjectReference Include="..\MqttBroker.Configuration\MqttBroker.Configuration.csproj" />
    <ProjectReference Include="..\MqttBroker.Logging\MqttBroker.Logging.csproj" />
    <ProjectReference Include="..\MqttBroker.Metrics\MqttBroker.Metrics.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
  </ItemGroup>

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
