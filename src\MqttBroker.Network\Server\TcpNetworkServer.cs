using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Network.Abstractions;
using MqttBroker.Network.Configuration;
using MqttBroker.Network.Connection;

namespace MqttBroker.Network.Server;

/// <summary>
/// TCP 网络服务器实现
/// </summary>
public class TcpNetworkServer : INetworkServer
{
    private readonly ILogger<TcpNetworkServer> _logger;
    private readonly NetworkConfiguration _configuration;
    private readonly IConnectionManager _connectionManager;
    private readonly IServiceProvider _serviceProvider;
    
    private TcpListener? _tcpListener;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _acceptTask;
    private readonly object _lockObject = new();
    private bool _disposed;

    // 统计信息
    private readonly NetworkServerStatistics _statistics = new();
    private readonly ConcurrentDictionary<string, DateTime> _connectionStartTimes = new();

    /// <summary>
    /// 初始化 TCP 网络服务器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">网络配置</param>
    /// <param name="connectionManager">连接管理器</param>
    /// <param name="serviceProvider">服务提供者</param>
    public TcpNetworkServer(
        ILogger<TcpNetworkServer> logger,
        IOptions<NetworkConfiguration> configuration,
        IConnectionManager connectionManager,
        IServiceProvider serviceProvider)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration?.Value ?? throw new ArgumentNullException(nameof(configuration));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

        _statistics.StartTime = DateTime.UtcNow;
        _statistics.LastActivity = DateTime.UtcNow;

        // 订阅连接管理器事件
        _connectionManager.ClientConnected += OnClientConnected;
        _connectionManager.ClientDisconnected += OnClientDisconnected;
    }

    /// <summary>
    /// 服务器是否正在运行
    /// </summary>
    public bool IsRunning { get; private set; }

    /// <summary>
    /// 监听的端点
    /// </summary>
    public EndPoint? LocalEndPoint => _tcpListener?.LocalEndpoint;

    /// <summary>
    /// 当前连接数
    /// </summary>
    public int ConnectionCount => _connectionManager.ConnectionCount;

    /// <summary>
    /// 客户端连接事件
    /// </summary>
    public event EventHandler<ClientConnectedEventArgs>? ClientConnected;

    /// <summary>
    /// 客户端断开连接事件
    /// </summary>
    public event EventHandler<ClientDisconnectedEventArgs>? ClientDisconnected;

    /// <summary>
    /// 启动服务器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (!_configuration.Tcp.Enabled)
        {
            _logger.LogInformation("TCP server is disabled in configuration");
            return;
        }

        lock (_lockObject)
        {
            if (IsRunning)
            {
                _logger.LogWarning("TCP server is already running");
                return;
            }

            try
            {
                var endPoint = new IPEndPoint(IPAddress.Parse(_configuration.Tcp.Address), _configuration.Tcp.Port);
                _tcpListener = new TcpListener(endPoint);
                _tcpListener.Server.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                
                if (_configuration.Tcp.NoDelay)
                {
                    _tcpListener.Server.SetSocketOption(SocketOptionLevel.Tcp, SocketOptionName.NoDelay, true);
                }

                _tcpListener.Start(_configuration.Tcp.Backlog);
                
                _cancellationTokenSource = new CancellationTokenSource();
                IsRunning = true;

                _logger.LogInformation("TCP server started on {EndPoint}", endPoint);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start TCP server");
                throw;
            }
        }

        // 启动接受连接的任务
        _acceptTask = AcceptConnectionsAsync(_cancellationTokenSource.Token);
        
        await Task.CompletedTask;
    }

    /// <summary>
    /// 停止服务器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        lock (_lockObject)
        {
            if (!IsRunning)
            {
                _logger.LogWarning("TCP server is not running");
                return;
            }

            IsRunning = false;
            _cancellationTokenSource?.Cancel();
            _tcpListener?.Stop();
        }

        try
        {
            if (_acceptTask != null)
            {
                await _acceptTask.ConfigureAwait(false);
            }
        }
        catch (OperationCanceledException)
        {
            // 预期的取消操作
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while stopping TCP server");
        }

        // 关闭所有连接
        await _connectionManager.CloseAllConnectionsAsync(DisconnectionReason.ServerShutdown, cancellationToken);

        _logger.LogInformation("TCP server stopped");
    }

    /// <summary>
    /// 获取服务器统计信息
    /// </summary>
    /// <returns>服务器统计信息</returns>
    public NetworkServerStatistics GetStatistics()
    {
        _statistics.CurrentConnections = ConnectionCount;
        _statistics.LastActivity = DateTime.UtcNow;
        
        // 计算平均连接持续时间
        if (_statistics.TotalDisconnections > 0)
        {
            var totalDuration = _connectionStartTimes.Values
                .Where(startTime => startTime != default)
                .Sum(startTime => (DateTime.UtcNow - startTime).TotalMilliseconds);
            
            _statistics.AverageConnectionDuration = totalDuration / _statistics.TotalDisconnections;
        }

        // 计算每秒连接数
        var uptime = DateTime.UtcNow - _statistics.StartTime;
        if (uptime.TotalSeconds > 0)
        {
            _statistics.ConnectionsPerSecond = _statistics.TotalConnections / uptime.TotalSeconds;
            _statistics.PacketsPerSecond = _statistics.PacketsReceived / uptime.TotalSeconds;
            _statistics.BytesPerSecond = _statistics.BytesReceived / uptime.TotalSeconds;
        }

        return _statistics;
    }

    /// <summary>
    /// 接受客户端连接的异步任务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接受连接任务</returns>
    private async Task AcceptConnectionsAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Started accepting TCP connections");

        try
        {
            while (!cancellationToken.IsCancellationRequested && IsRunning)
            {
                try
                {
                    var tcpClient = await _tcpListener!.AcceptTcpClientAsync().ConfigureAwait(false);
                    
                    // 检查是否可以接受新连接
                    if (!_connectionManager.CanAcceptConnection())
                    {
                        _logger.LogWarning("Connection limit reached, rejecting connection from {RemoteEndPoint}", 
                            tcpClient.Client.RemoteEndPoint);
                        
                        _statistics.Errors++;
                        tcpClient.Close();
                        continue;
                    }

                    // 在后台处理连接
                    _ = Task.Run(async () => await HandleClientConnectionAsync(tcpClient, cancellationToken), cancellationToken);
                }
                catch (ObjectDisposedException)
                {
                    // TCP 监听器已被释放，正常退出
                    break;
                }
                catch (SocketException ex) when (ex.SocketErrorCode == SocketError.Interrupted)
                {
                    // 监听器被中断，正常退出
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error accepting TCP connection");
                    _statistics.Errors++;
                    
                    // 短暂延迟以避免快速循环
                    await Task.Delay(100, cancellationToken);
                }
            }
        }
        catch (OperationCanceledException)
        {
            // 预期的取消操作
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in TCP connection acceptance loop");
        }

        _logger.LogDebug("Stopped accepting TCP connections");
    }

    /// <summary>
    /// 处理客户端连接
    /// </summary>
    /// <param name="tcpClient">TCP 客户端</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    private async Task HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken)
    {
        IClientConnection? connection = null;
        
        try
        {
            // 配置 TCP 客户端
            tcpClient.ReceiveBufferSize = _configuration.Connection.ReceiveBufferSize;
            tcpClient.SendBufferSize = _configuration.Connection.SendBufferSize;
            
            if (_configuration.Tcp.NoDelay)
            {
                tcpClient.NoDelay = true;
            }

            // 创建客户端连接
            connection = ActivatorUtilities.CreateInstance<TcpClientConnection>(_serviceProvider, tcpClient);
            
            // 添加到连接管理器
            if (_connectionManager.AddConnection(connection))
            {
                _logger.LogDebug("Accepted TCP connection from {RemoteEndPoint} with ID {ConnectionId}", 
                    connection.RemoteEndPoint, connection.Id);
                
                _statistics.TotalConnections++;
                _connectionStartTimes[connection.Id] = DateTime.UtcNow;
                
                // 启动连接处理
                if (connection is TcpClientConnection tcpConnection)
                {
                    await tcpConnection.StartAsync(cancellationToken);
                }
            }
            else
            {
                _logger.LogWarning("Failed to add connection {ConnectionId} to connection manager", connection.Id);
                await connection.CloseAsync(DisconnectionReason.ServerDisconnected, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling TCP client connection");
            _statistics.Errors++;
            
            if (connection != null)
            {
                await connection.CloseAsync(DisconnectionReason.NetworkError, cancellationToken);
            }
            else
            {
                tcpClient.Close();
            }
        }
    }

    /// <summary>
    /// 处理客户端连接事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnClientConnected(object? sender, ClientConnectedEventArgs e)
    {
        ClientConnected?.Invoke(this, e);
    }

    /// <summary>
    /// 处理客户端断开连接事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnClientDisconnected(object? sender, ClientDisconnectedEventArgs e)
    {
        _statistics.TotalDisconnections++;
        _connectionStartTimes.TryRemove(e.Connection.Id, out _);
        
        ClientDisconnected?.Invoke(this, e);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            StopAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during TCP server disposal");
        }

        _cancellationTokenSource?.Dispose();
        _tcpListener = null;

        // 取消订阅事件
        if (_connectionManager != null)
        {
            _connectionManager.ClientConnected -= OnClientConnected;
            _connectionManager.ClientDisconnected -= OnClientDisconnected;
        }

        GC.SuppressFinalize(this);
    }
}
