# MQTT Broker 启动脚本
# 用法: .\scripts\start.ps1 [Development|Production]

param(
    [string]$Environment = "Development"
)

Write-Host "正在启动 MQTT Broker 服务器..." -ForegroundColor Green
Write-Host "环境: $Environment" -ForegroundColor Yellow

# 确保数据目录存在
if (!(Test-Path "data")) {
    New-Item -ItemType Directory -Path "data"
    Write-Host "已创建数据目录: data/" -ForegroundColor Green
}

# 构建项目
Write-Host "正在构建项目..." -ForegroundColor Yellow
dotnet build MqttBroker.sln

if ($LASTEXITCODE -eq 0) {
    Write-Host "构建成功!" -ForegroundColor Green
    
    # 启动服务器
    Write-Host "正在启动 MQTT Broker 服务器..." -ForegroundColor Yellow
    dotnet run --project src/MqttBroker.Host --environment $Environment
} else {
    Write-Host "构建失败!" -ForegroundColor Red
    exit 1
}
