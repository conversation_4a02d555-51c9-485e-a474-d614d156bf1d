using System;
using System.Buffers;
using System.Text;

namespace MqttBroker.Core.Protocol
{
    /// <summary>
    /// MQTT 二进制数据写入器
    /// </summary>
    public ref struct MqttBinaryWriter
    {
        private Span<byte> _buffer;
        private int _position;

        /// <summary>
        /// 初始化 MQTT 二进制写入器
        /// </summary>
        /// <param name="buffer">要写入的缓冲区</param>
        public MqttBinaryWriter(Span<byte> buffer)
        {
            _buffer = buffer;
            _position = 0;
        }

        /// <summary>
        /// 获取已写入的字节数
        /// </summary>
        public int Written => _position;

        /// <summary>
        /// 获取剩余的缓冲区空间
        /// </summary>
        public int Remaining => _buffer.Length - _position;

        /// <summary>
        /// 检查是否还有指定数量的空间可写
        /// </summary>
        /// <param name="count">要检查的字节数</param>
        /// <returns>如果有足够的空间则返回 true，否则返回 false</returns>
        public bool HasSpace(int count)
        {
            return Remaining >= count;
        }

        /// <summary>
        /// 写入一个字节
        /// </summary>
        /// <param name="value">要写入的字节</param>
        /// <exception cref="InvalidOperationException">当没有足够的空间时抛出</exception>
        public void WriteByte(byte value)
        {
            if (!HasSpace(1))
                throw new InvalidOperationException("Not enough space to write a byte");

            _buffer[_position] = value;
            _position++;
        }

        /// <summary>
        /// 写入 ushort 值 (大端序)
        /// </summary>
        /// <param name="value">要写入的 ushort 值</param>
        /// <exception cref="InvalidOperationException">当没有足够的空间时抛出</exception>
        public void WriteUInt16(ushort value)
        {
            if (!HasSpace(2))
                throw new InvalidOperationException("Not enough space to write a UInt16");

            _buffer[_position] = (byte)(value >> 8);
            _buffer[_position + 1] = (byte)(value & 0xFF);
            _position += 2;
        }

        /// <summary>
        /// 写入 uint 值 (大端序)
        /// </summary>
        /// <param name="value">要写入的 uint 值</param>
        /// <exception cref="InvalidOperationException">当没有足够的空间时抛出</exception>
        public void WriteUInt32(uint value)
        {
            if (!HasSpace(4))
                throw new InvalidOperationException("Not enough space to write a UInt32");

            _buffer[_position] = (byte)(value >> 24);
            _buffer[_position + 1] = (byte)((value >> 16) & 0xFF);
            _buffer[_position + 2] = (byte)((value >> 8) & 0xFF);
            _buffer[_position + 3] = (byte)(value & 0xFF);
            _position += 4;
        }

        /// <summary>
        /// 写入可变长度整数
        /// </summary>
        /// <param name="value">要写入的整数值</param>
        /// <exception cref="ArgumentOutOfRangeException">当值超出范围时抛出</exception>
        /// <exception cref="InvalidOperationException">当没有足够的空间时抛出</exception>
        public void WriteVariableByteInteger(int value)
        {
            if (value < 0 || value > MqttProtocolConstants.PacketSizeLimits.MaxPacketSize)
                throw new ArgumentOutOfRangeException(nameof(value));

            do
            {
                if (!HasSpace(1))
                    throw new InvalidOperationException("Not enough space to write variable byte integer");

                var encodedByte = (byte)(value % 128);
                value /= 128;

                if (value > 0)
                    encodedByte |= 0x80;

                WriteByte(encodedByte);
            }
            while (value > 0);
        }

        /// <summary>
        /// 写入 UTF-8 字符串（带长度前缀）
        /// </summary>
        /// <param name="value">要写入的字符串</param>
        /// <exception cref="InvalidOperationException">当没有足够的空间时抛出</exception>
        public void WriteString(string? value)
        {
            if (string.IsNullOrEmpty(value))
            {
                WriteUInt16(0);
                return;
            }

            var utf8Bytes = Encoding.UTF8.GetBytes(value);
            if (utf8Bytes.Length > ushort.MaxValue)
                throw new ArgumentException($"String is too long: {utf8Bytes.Length} bytes");

            WriteUInt16((ushort)utf8Bytes.Length);
            WriteBytes(utf8Bytes);
        }

        /// <summary>
        /// 写入二进制数据（带长度前缀）
        /// </summary>
        /// <param name="data">要写入的字节数组</param>
        /// <exception cref="InvalidOperationException">当没有足够的空间时抛出</exception>
        public void WriteBinaryData(byte[]? data)
        {
            if (data == null || data.Length == 0)
            {
                WriteUInt16(0);
                return;
            }

            if (data.Length > ushort.MaxValue)
                throw new ArgumentException($"Binary data is too long: {data.Length} bytes");

            WriteUInt16((ushort)data.Length);
            WriteBytes(data);
        }

        /// <summary>
        /// 写入字节数组
        /// </summary>
        /// <param name="bytes">要写入的字节数组</param>
        /// <exception cref="InvalidOperationException">当没有足够的空间时抛出</exception>
        public void WriteBytes(byte[] bytes)
        {
            if (bytes == null || bytes.Length == 0)
                return;

            if (!HasSpace(bytes.Length))
                throw new InvalidOperationException($"Not enough space to write {bytes.Length} bytes");

            bytes.CopyTo(_buffer.Slice(_position));
            _position += bytes.Length;
        }

        /// <summary>
        /// 写入字节跨度
        /// </summary>
        /// <param name="bytes">要写入的字节跨度</param>
        /// <exception cref="InvalidOperationException">当没有足够的空间时抛出</exception>
        public void WriteBytes(ReadOnlySpan<byte> bytes)
        {
            if (bytes.Length == 0)
                return;

            if (!HasSpace(bytes.Length))
                throw new InvalidOperationException($"Not enough space to write {bytes.Length} bytes");

            bytes.CopyTo(_buffer.Slice(_position));
            _position += bytes.Length;
        }

        /// <summary>
        /// 在指定位置写入字节
        /// </summary>
        /// <param name="position">写入位置</param>
        /// <param name="value">要写入的字节</param>
        /// <exception cref="ArgumentOutOfRangeException">当位置超出范围时抛出</exception>
        public void WriteByteAt(int position, byte value)
        {
            if (position < 0 || position >= _buffer.Length)
                throw new ArgumentOutOfRangeException(nameof(position));

            _buffer[position] = value;
        }

        /// <summary>
        /// 在指定位置写入 ushort 值 (大端序)
        /// </summary>
        /// <param name="position">写入位置</param>
        /// <param name="value">要写入的 ushort 值</param>
        /// <exception cref="ArgumentOutOfRangeException">当位置超出范围时抛出</exception>
        public void WriteUInt16At(int position, ushort value)
        {
            if (position < 0 || position + 1 >= _buffer.Length)
                throw new ArgumentOutOfRangeException(nameof(position));

            _buffer[position] = (byte)(value >> 8);
            _buffer[position + 1] = (byte)(value & 0xFF);
        }

        /// <summary>
        /// 获取已写入的字节数组
        /// </summary>
        /// <returns>已写入的字节数组</returns>
        public byte[] ToArray()
        {
            return _buffer.Slice(0, _position).ToArray();
        }

        /// <summary>
        /// 获取已写入的字节跨度
        /// </summary>
        /// <returns>已写入的字节跨度</returns>
        public ReadOnlySpan<byte> WrittenSpan => _buffer.Slice(0, _position);

        /// <summary>
        /// 重置写入器
        /// </summary>
        public void Reset()
        {
            _position = 0;
        }

        /// <summary>
        /// 设置写入位置
        /// </summary>
        /// <param name="position">新的写入位置</param>
        /// <exception cref="ArgumentOutOfRangeException">当位置超出范围时抛出</exception>
        public void Seek(int position)
        {
            if (position < 0 || position > _buffer.Length)
                throw new ArgumentOutOfRangeException(nameof(position));

            _position = position;
        }
    }
}
