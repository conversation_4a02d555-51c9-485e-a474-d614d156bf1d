using System;
using System.Collections.Generic;
using System.Linq;

namespace MqttBroker.Core.Protocol.Packets
{
    /// <summary>
    /// MQTT UNSUBSCRIBE 数据包
    /// </summary>
    public class MqttUnsubscribePacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.Unsubscribe;

        /// <summary>
        /// 数据包标志位 (UNSUBSCRIBE 固定为 0x02)
        /// </summary>
        public override byte Flags => 0x02;

        /// <summary>
        /// 要取消订阅的主题过滤器列表
        /// </summary>
        public List<string> TopicFilters { get; set; } = new();

        /// <summary>
        /// MQTT 5.0 属性（仅适用于 MQTT 5.0）
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            int size = 1; // 固定头部第一个字节
            size += 2; // 数据包标识符

            // MQTT 5.0 属性
            if (Properties != null)
            {
                size += Properties.GetEstimatedSize();
            }

            // 有效载荷 - 主题过滤器列表
            foreach (var topicFilter in TopicFilters)
            {
                size += GetStringLength(topicFilter);
            }

            // 剩余长度字段
            size += GetVariableByteIntegerLength(size - 1);

            return size;
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // UNSUBSCRIBE 必须有数据包标识符
            if (!IsPacketIdentifierValid())
                return false;

            // 必须至少有一个主题过滤器
            if (TopicFilters == null || TopicFilters.Count == 0)
                return false;

            // 验证所有主题过滤器
            return TopicFilters.All(IsTopicFilterValid);
        }

        /// <summary>
        /// 创建 UNSUBSCRIBE 数据包
        /// </summary>
        /// <param name="packetIdentifier">数据包标识符</param>
        /// <param name="topicFilters">主题过滤器列表</param>
        /// <returns>UNSUBSCRIBE 数据包</returns>
        public static MqttUnsubscribePacket Create(ushort packetIdentifier, params string[] topicFilters)
        {
            return new MqttUnsubscribePacket
            {
                PacketIdentifier = packetIdentifier,
                TopicFilters = topicFilters.ToList()
            };
        }

        /// <summary>
        /// 添加主题过滤器
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        public void AddTopicFilter(string topicFilter)
        {
            if (IsTopicFilterValid(topicFilter))
            {
                TopicFilters.Add(topicFilter);
            }
            else
            {
                throw new ArgumentException($"Invalid topic filter: {topicFilter}", nameof(topicFilter));
            }
        }
    }

    /// <summary>
    /// MQTT UNSUBACK 数据包
    /// </summary>
    public class MqttUnsubAckPacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.UnsubAck;

        /// <summary>
        /// 原因码列表 (MQTT 5.0) 或空载荷 (MQTT 3.1.1)
        /// </summary>
        public List<MqttReasonCode> ReasonCodes { get; set; } = new();

        /// <summary>
        /// MQTT 5.0 属性（仅适用于 MQTT 5.0）
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            int size = 1; // 固定头部第一个字节
            size += 2; // 数据包标识符

            // MQTT 5.0 属性
            if (Properties != null)
            {
                size += Properties.GetEstimatedSize();
            }

            // 有效载荷 - 原因码列表 (仅 MQTT 5.0)
            if (ReasonCodes.Count > 0)
            {
                size += ReasonCodes.Count;
            }

            // 剩余长度字段
            size += GetVariableByteIntegerLength(size - 1);

            return size;
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // UNSUBACK 必须有数据包标识符
            return IsPacketIdentifierValid();
        }

        /// <summary>
        /// 创建 UNSUBACK 数据包 (MQTT 3.1.1)
        /// </summary>
        /// <param name="packetIdentifier">数据包标识符</param>
        /// <returns>UNSUBACK 数据包</returns>
        public static MqttUnsubAckPacket Create(ushort packetIdentifier)
        {
            return new MqttUnsubAckPacket
            {
                PacketIdentifier = packetIdentifier
            };
        }

        /// <summary>
        /// 创建 UNSUBACK 数据包 (MQTT 5.0)
        /// </summary>
        /// <param name="packetIdentifier">数据包标识符</param>
        /// <param name="reasonCodes">原因码列表</param>
        /// <returns>UNSUBACK 数据包</returns>
        public static MqttUnsubAckPacket Create(ushort packetIdentifier, params MqttReasonCode[] reasonCodes)
        {
            return new MqttUnsubAckPacket
            {
                PacketIdentifier = packetIdentifier,
                ReasonCodes = reasonCodes.ToList()
            };
        }

        /// <summary>
        /// 创建成功的 UNSUBACK 数据包
        /// </summary>
        /// <param name="packetIdentifier">数据包标识符</param>
        /// <param name="topicFilterCount">取消订阅的主题过滤器数量</param>
        /// <returns>UNSUBACK 数据包</returns>
        public static MqttUnsubAckPacket CreateSuccess(ushort packetIdentifier, int topicFilterCount)
        {
            var reasonCodes = Enumerable.Repeat(MqttReasonCode.Success, topicFilterCount).ToArray();
            return Create(packetIdentifier, reasonCodes);
        }

        /// <summary>
        /// 创建部分成功的 UNSUBACK 数据包
        /// </summary>
        /// <param name="packetIdentifier">数据包标识符</param>
        /// <param name="results">每个主题过滤器的处理结果</param>
        /// <returns>UNSUBACK 数据包</returns>
        public static MqttUnsubAckPacket CreatePartialSuccess(ushort packetIdentifier, params bool[] results)
        {
            var reasonCodes = results.Select(success => 
                success ? MqttReasonCode.Success : MqttReasonCode.NoSubscriptionExisted).ToArray();
            return Create(packetIdentifier, reasonCodes);
        }
    }
}
