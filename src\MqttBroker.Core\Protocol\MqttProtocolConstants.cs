using System;

namespace MqttBroker.Core.Protocol
{
    /// <summary>
    /// MQTT 协议常量定义
    /// </summary>
    public static class MqttProtocolConstants
    {
        /// <summary>
        /// MQTT 协议版本
        /// </summary>
        public static class ProtocolVersions
        {
            /// <summary>
            /// MQTT 3.1.1 协议版本号
            /// </summary>
            public const byte Mqtt311 = 4;

            /// <summary>
            /// MQTT 5.0 协议版本号
            /// </summary>
            public const byte Mqtt50 = 5;
        }

        /// <summary>
        /// MQTT 协议名称
        /// </summary>
        public static class ProtocolNames
        {
            /// <summary>
            /// MQTT 3.1.1 协议名称
            /// </summary>
            public const string Mqtt311 = "MQTT";

            /// <summary>
            /// MQTT 5.0 协议名称
            /// </summary>
            public const string Mqtt50 = "MQTT";
        }

        /// <summary>
        /// 数据包类型掩码
        /// </summary>
        public static class PacketTypeMasks
        {
            /// <summary>
            /// 数据包类型掩码 (高4位)
            /// </summary>
            public const byte PacketType = 0xF0;

            /// <summary>
            /// 标志位掩码 (低4位)
            /// </summary>
            public const byte Flags = 0x0F;
        }

        /// <summary>
        /// 最大数据包大小限制
        /// </summary>
        public static class PacketSizeLimits
        {
            /// <summary>
            /// 最大数据包大小 (256MB)
            /// </summary>
            public const int MaxPacketSize = 268435455; // 0x0FFFFFFF

            /// <summary>
            /// 最大主题长度
            /// </summary>
            public const int MaxTopicLength = 65535;

            /// <summary>
            /// 最大客户端ID长度
            /// </summary>
            public const int MaxClientIdLength = 65535;

            /// <summary>
            /// 最大用户名长度
            /// </summary>
            public const int MaxUsernameLength = 65535;

            /// <summary>
            /// 最大密码长度
            /// </summary>
            public const int MaxPasswordLength = 65535;
        }

        /// <summary>
        /// QoS 级别常量
        /// </summary>
        public static class QoSLevels
        {
            /// <summary>
            /// QoS 0 - 最多一次传递
            /// </summary>
            public const byte AtMostOnce = 0;

            /// <summary>
            /// QoS 1 - 至少一次传递
            /// </summary>
            public const byte AtLeastOnce = 1;

            /// <summary>
            /// QoS 2 - 恰好一次传递
            /// </summary>
            public const byte ExactlyOnce = 2;
        }

        /// <summary>
        /// 连接标志位
        /// </summary>
        public static class ConnectFlags
        {
            /// <summary>
            /// 用户名标志
            /// </summary>
            public const byte UsernameFlag = 0x80;

            /// <summary>
            /// 密码标志
            /// </summary>
            public const byte PasswordFlag = 0x40;

            /// <summary>
            /// 遗嘱保留标志
            /// </summary>
            public const byte WillRetainFlag = 0x20;

            /// <summary>
            /// 遗嘱QoS标志掩码
            /// </summary>
            public const byte WillQoSMask = 0x18;

            /// <summary>
            /// 遗嘱标志
            /// </summary>
            public const byte WillFlag = 0x04;

            /// <summary>
            /// 清理会话标志
            /// </summary>
            public const byte CleanSessionFlag = 0x02;
        }

        /// <summary>
        /// 默认端口号
        /// </summary>
        public static class DefaultPorts
        {
            /// <summary>
            /// MQTT 默认端口
            /// </summary>
            public const int Mqtt = 1883;

            /// <summary>
            /// MQTT over TLS 默认端口
            /// </summary>
            public const int MqttTls = 8883;

            /// <summary>
            /// MQTT over WebSocket 默认端口
            /// </summary>
            public const int MqttWebSocket = 8080;

            /// <summary>
            /// MQTT over WebSocket Secure 默认端口
            /// </summary>
            public const int MqttWebSocketSecure = 8443;
        }

        /// <summary>
        /// 保活时间限制
        /// </summary>
        public static class KeepAliveLimits
        {
            /// <summary>
            /// 最小保活时间 (秒)
            /// </summary>
            public const ushort MinKeepAlive = 0;

            /// <summary>
            /// 最大保活时间 (秒)
            /// </summary>
            public const ushort MaxKeepAlive = 65535;

            /// <summary>
            /// 默认保活时间 (秒)
            /// </summary>
            public const ushort DefaultKeepAlive = 60;
        }

        /// <summary>
        /// 主题通配符
        /// </summary>
        public static class TopicWildcards
        {
            /// <summary>
            /// 单级通配符
            /// </summary>
            public const char SingleLevel = '+';

            /// <summary>
            /// 多级通配符
            /// </summary>
            public const char MultiLevel = '#';

            /// <summary>
            /// 主题分隔符
            /// </summary>
            public const char Separator = '/';
        }

        /// <summary>
        /// 系统主题前缀
        /// </summary>
        public static class SystemTopics
        {
            /// <summary>
            /// 系统主题前缀
            /// </summary>
            public const string Prefix = "$SYS/";
        }
    }
}
