using System;
using System.Collections.Generic;
using System.Linq;

namespace MqttBroker.Core.Protocol
{
    /// <summary>
    /// MQTT 主题匹配器接口
    /// </summary>
    public interface IMqttTopicMatcher
    {
        /// <summary>
        /// 检查主题名称是否匹配主题过滤器
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        /// <param name="topicName">主题名称</param>
        /// <returns>如果匹配则返回 true，否则返回 false</returns>
        bool IsMatch(string topicFilter, string topicName);

        /// <summary>
        /// 从主题过滤器列表中找到匹配指定主题名称的过滤器
        /// </summary>
        /// <param name="topicFilters">主题过滤器列表</param>
        /// <param name="topicName">主题名称</param>
        /// <returns>匹配的主题过滤器列表</returns>
        IEnumerable<string> GetMatchingFilters(IEnumerable<string> topicFilters, string topicName);

        /// <summary>
        /// 验证主题过滤器的有效性
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        /// <returns>如果有效则返回 true，否则返回 false</returns>
        bool IsValidTopicFilter(string topicFilter);

        /// <summary>
        /// 验证主题名称的有效性
        /// </summary>
        /// <param name="topicName">主题名称</param>
        /// <returns>如果有效则返回 true，否则返回 false</returns>
        bool IsValidTopicName(string topicName);
    }

    /// <summary>
    /// MQTT 主题匹配器实现
    /// </summary>
    public class MqttTopicMatcher : IMqttTopicMatcher
    {
        /// <summary>
        /// 检查主题名称是否匹配主题过滤器
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        /// <param name="topicName">主题名称</param>
        /// <returns>如果匹配则返回 true，否则返回 false</returns>
        public bool IsMatch(string topicFilter, string topicName)
        {
            if (topicFilter == null || topicName == null)
                return false;

            // 特殊情况：单独的多级通配符匹配任何非系统主题（包括空主题）
            if (topicFilter == "#")
            {
                // 但不匹配系统主题
                if (!string.IsNullOrEmpty(topicName) && topicName.StartsWith(MqttProtocolConstants.SystemTopics.Prefix))
                    return false;
                return true;
            }

            // 空字符串的特殊处理
            if (topicFilter == "" && topicName == "")
                return true;

            if (string.IsNullOrEmpty(topicFilter) || string.IsNullOrEmpty(topicName))
                return false;

            // 系统主题的特殊处理
            if (topicName.StartsWith(MqttProtocolConstants.SystemTopics.Prefix))
            {
                // 系统主题只能被明确订阅，不能使用通配符
                if (topicFilter.Contains(MqttProtocolConstants.TopicWildcards.SingleLevel) ||
                    topicFilter.Contains(MqttProtocolConstants.TopicWildcards.MultiLevel))
                {
                    return false;
                }
                return topicFilter == topicName;
            }



            // 如果过滤器不包含通配符，直接比较
            if (!topicFilter.Contains(MqttProtocolConstants.TopicWildcards.SingleLevel) &&
                !topicFilter.Contains(MqttProtocolConstants.TopicWildcards.MultiLevel))
            {
                return topicFilter == topicName;
            }

            return MatchWithWildcards(topicFilter, topicName);
        }

        /// <summary>
        /// 从主题过滤器列表中找到匹配指定主题名称的过滤器
        /// </summary>
        /// <param name="topicFilters">主题过滤器列表</param>
        /// <param name="topicName">主题名称</param>
        /// <returns>匹配的主题过滤器列表</returns>
        public IEnumerable<string> GetMatchingFilters(IEnumerable<string> topicFilters, string topicName)
        {
            if (topicFilters == null || string.IsNullOrEmpty(topicName))
                return Enumerable.Empty<string>();

            return topicFilters.Where(filter => IsMatch(filter, topicName));
        }

        /// <summary>
        /// 验证主题过滤器的有效性
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        /// <returns>如果有效则返回 true，否则返回 false</returns>
        public bool IsValidTopicFilter(string topicFilter)
        {
            if (string.IsNullOrEmpty(topicFilter))
                return false;

            if (topicFilter.Length > MqttProtocolConstants.PacketSizeLimits.MaxTopicLength)
                return false;

            // 主题过滤器不能包含空字符
            if (topicFilter.Contains('\0'))
                return false;

            // 验证通配符使用规则
            var parts = topicFilter.Split(MqttProtocolConstants.TopicWildcards.Separator);
            
            for (int i = 0; i < parts.Length; i++)
            {
                var part = parts[i];
                
                // 检查单级通配符
                if (part.Contains(MqttProtocolConstants.TopicWildcards.SingleLevel))
                {
                    // 单级通配符必须单独占据一个级别
                    if (part.Length != 1)
                        return false;
                }
                
                // 检查多级通配符
                if (part.Contains(MqttProtocolConstants.TopicWildcards.MultiLevel))
                {
                    // 多级通配符必须单独占据一个级别，且必须是最后一个级别
                    if (part.Length != 1 || i != parts.Length - 1)
                        return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 验证主题名称的有效性
        /// </summary>
        /// <param name="topicName">主题名称</param>
        /// <returns>如果有效则返回 true，否则返回 false</returns>
        public bool IsValidTopicName(string topicName)
        {
            if (string.IsNullOrEmpty(topicName))
                return false;

            if (topicName.Length > MqttProtocolConstants.PacketSizeLimits.MaxTopicLength)
                return false;

            // 主题名称不能包含通配符
            if (topicName.Contains(MqttProtocolConstants.TopicWildcards.SingleLevel) ||
                topicName.Contains(MqttProtocolConstants.TopicWildcards.MultiLevel))
                return false;

            // 主题名称不能包含空字符
            if (topicName.Contains('\0'))
                return false;

            return true;
        }

        #region 私有方法

        /// <summary>
        /// 使用通配符进行主题匹配
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        /// <param name="topicName">主题名称</param>
        /// <returns>如果匹配则返回 true，否则返回 false</returns>
        private bool MatchWithWildcards(string topicFilter, string topicName)
        {
            var filterParts = topicFilter.Split(MqttProtocolConstants.TopicWildcards.Separator);
            var nameParts = topicName.Split(MqttProtocolConstants.TopicWildcards.Separator);

            return MatchParts(filterParts, nameParts, 0, 0);
        }

        /// <summary>
        /// 递归匹配主题部分
        /// </summary>
        /// <param name="filterParts">过滤器部分数组</param>
        /// <param name="nameParts">名称部分数组</param>
        /// <param name="filterIndex">过滤器当前索引</param>
        /// <param name="nameIndex">名称当前索引</param>
        /// <returns>如果匹配则返回 true，否则返回 false</returns>
        private bool MatchParts(string[] filterParts, string[] nameParts, int filterIndex, int nameIndex)
        {
            // 如果过滤器已经处理完
            if (filterIndex >= filterParts.Length)
            {
                // 名称也必须处理完才算匹配
                return nameIndex >= nameParts.Length;
            }

            var filterPart = filterParts[filterIndex];

            // 处理多级通配符
            if (filterPart == MqttProtocolConstants.TopicWildcards.MultiLevel.ToString())
            {
                // 多级通配符匹配剩余的所有级别（包括空）
                // 但是多级通配符必须是最后一个部分
                return filterIndex == filterParts.Length - 1;
            }

            // 如果名称已经处理完，但过滤器还有非多级通配符的部分
            if (nameIndex >= nameParts.Length)
            {
                return false;
            }

            var namePart = nameParts[nameIndex];

            // 处理单级通配符
            if (filterPart == MqttProtocolConstants.TopicWildcards.SingleLevel.ToString())
            {
                // 单级通配符匹配任何单个级别（除了空级别）
                return MatchParts(filterParts, nameParts, filterIndex + 1, nameIndex + 1);
            }

            // 精确匹配
            if (filterPart == namePart)
            {
                return MatchParts(filterParts, nameParts, filterIndex + 1, nameIndex + 1);
            }

            // 不匹配
            return false;
        }

        #endregion
    }

    /// <summary>
    /// 主题匹配结果
    /// </summary>
    public class TopicMatchResult
    {
        /// <summary>
        /// 是否匹配
        /// </summary>
        public bool IsMatch { get; set; }

        /// <summary>
        /// 匹配的主题过滤器
        /// </summary>
        public string? MatchedFilter { get; set; }

        /// <summary>
        /// 匹配的主题名称
        /// </summary>
        public string? TopicName { get; set; }

        /// <summary>
        /// 创建匹配结果
        /// </summary>
        /// <param name="isMatch">是否匹配</param>
        /// <param name="matchedFilter">匹配的过滤器</param>
        /// <param name="topicName">主题名称</param>
        /// <returns>匹配结果</returns>
        public static TopicMatchResult Create(bool isMatch, string? matchedFilter = null, string? topicName = null)
        {
            return new TopicMatchResult
            {
                IsMatch = isMatch,
                MatchedFilter = matchedFilter,
                TopicName = topicName
            };
        }

        /// <summary>
        /// 创建成功匹配的结果
        /// </summary>
        /// <param name="matchedFilter">匹配的过滤器</param>
        /// <param name="topicName">主题名称</param>
        /// <returns>匹配结果</returns>
        public static TopicMatchResult Success(string matchedFilter, string topicName)
        {
            return Create(true, matchedFilter, topicName);
        }

        /// <summary>
        /// 创建不匹配的结果
        /// </summary>
        /// <returns>不匹配的结果</returns>
        public static TopicMatchResult Failure()
        {
            return Create(false);
        }
    }
}
