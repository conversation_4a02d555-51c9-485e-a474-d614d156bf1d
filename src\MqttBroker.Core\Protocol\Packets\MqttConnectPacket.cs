using System;
using System.Collections.Generic;

namespace MqttBroker.Core.Protocol.Packets
{
    /// <summary>
    /// MQTT CONNECT 数据包
    /// </summary>
    public class MqttConnectPacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.Connect;

        /// <summary>
        /// 协议名称
        /// </summary>
        public string ProtocolName { get; set; } = string.Empty;

        /// <summary>
        /// 协议版本
        /// </summary>
        public MqttProtocolVersion ProtocolVersion { get; set; }

        /// <summary>
        /// 客户端标识符
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 清理会话标志
        /// </summary>
        public bool CleanSession { get; set; }

        /// <summary>
        /// 保活时间（秒）
        /// </summary>
        public ushort KeepAlive { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string? Password { get; set; }

        /// <summary>
        /// 遗嘱消息
        /// </summary>
        public MqttWillMessage? WillMessage { get; set; }

        /// <summary>
        /// MQTT 5.0 属性（仅适用于 MQTT 5.0）
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 获取连接标志字节
        /// </summary>
        public byte ConnectFlags
        {
            get
            {
                byte flags = 0;

                if (!string.IsNullOrEmpty(Username))
                    flags |= MqttProtocolConstants.ConnectFlags.UsernameFlag;

                if (!string.IsNullOrEmpty(Password))
                    flags |= MqttProtocolConstants.ConnectFlags.PasswordFlag;

                if (WillMessage != null)
                {
                    flags |= MqttProtocolConstants.ConnectFlags.WillFlag;
                    
                    if (WillMessage.Retain)
                        flags |= MqttProtocolConstants.ConnectFlags.WillRetainFlag;

                    flags |= (byte)((byte)WillMessage.QoSLevel << 3);
                }

                if (CleanSession)
                    flags |= MqttProtocolConstants.ConnectFlags.CleanSessionFlag;

                return flags;
            }
        }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            int size = 1; // 固定头部第一个字节

            // 可变头部
            size += GetStringLength(ProtocolName); // 协议名称
            size += 1; // 协议版本
            size += 1; // 连接标志
            size += 2; // 保活时间

            // MQTT 5.0 属性
            if (ProtocolVersion == MqttProtocolVersion.Version50 && Properties != null)
            {
                size += Properties.GetEstimatedSize();
            }

            // 有效载荷
            size += GetStringLength(ClientId); // 客户端ID

            if (WillMessage != null)
            {
                // MQTT 5.0 遗嘱属性
                if (ProtocolVersion == MqttProtocolVersion.Version50 && WillMessage.Properties != null)
                {
                    size += WillMessage.Properties.GetEstimatedSize();
                }

                size += GetStringLength(WillMessage.Topic); // 遗嘱主题
                size += GetBinaryDataLength(WillMessage.Payload); // 遗嘱载荷
            }

            if (!string.IsNullOrEmpty(Username))
                size += GetStringLength(Username);

            if (!string.IsNullOrEmpty(Password))
                size += GetStringLength(Password);

            // 剩余长度字段
            size += GetVariableByteIntegerLength(size - 1);

            return size;
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // 验证协议名称
            if (string.IsNullOrEmpty(ProtocolName))
                return false;

            // 验证协议版本
            if (ProtocolVersion != MqttProtocolVersion.Version311 && 
                ProtocolVersion != MqttProtocolVersion.Version50)
                return false;

            // 验证客户端ID
            if (!IsClientIdValid(ClientId))
                return false;

            // 验证保活时间
            if (!IsKeepAliveValid(KeepAlive))
                return false;

            // 验证用户名
            if (!IsUsernameValid(Username))
                return false;

            // 验证密码
            if (!IsPasswordValid(Password))
                return false;

            // 如果有密码，必须有用户名
            if (!string.IsNullOrEmpty(Password) && string.IsNullOrEmpty(Username))
                return false;

            // 验证遗嘱消息
            if (WillMessage != null && !WillMessage.IsValid())
                return false;

            return true;
        }
    }

    /// <summary>
    /// MQTT 遗嘱消息
    /// </summary>
    public class MqttWillMessage
    {
        /// <summary>
        /// 遗嘱主题
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 遗嘱载荷
        /// </summary>
        public byte[] Payload { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// QoS 级别
        /// </summary>
        public MqttQoSLevel QoSLevel { get; set; }

        /// <summary>
        /// 保留标志
        /// </summary>
        public bool Retain { get; set; }

        /// <summary>
        /// MQTT 5.0 遗嘱属性（仅适用于 MQTT 5.0）
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 验证遗嘱消息的有效性
        /// </summary>
        /// <returns>如果遗嘱消息有效则返回 true，否则返回 false</returns>
        public bool IsValid()
        {
            // 验证主题
            if (!MqttPacketBase.IsTopicValid(Topic))
                return false;

            // 验证QoS级别
            if (!MqttPacketBase.IsQoSLevelValid(QoSLevel))
                return false;

            return true;
        }
    }

    /// <summary>
    /// MQTT 5.0 属性集合
    /// </summary>
    public class MqttProperties
    {
        /// <summary>
        /// 属性字典
        /// </summary>
        public Dictionary<MqttPropertyType, object> Properties { get; set; } = new();

        /// <summary>
        /// 获取属性的估计大小
        /// </summary>
        /// <returns>属性大小（字节）</returns>
        public int GetEstimatedSize()
        {
            int size = 0;

            foreach (var property in Properties)
            {
                size += 1; // 属性标识符
                size += GetPropertyValueSize(property.Key, property.Value);
            }

            // 属性长度字段
            size += MqttPacketBase.GetVariableByteIntegerLength(size);

            return size;
        }

        /// <summary>
        /// 获取属性值的大小
        /// </summary>
        /// <param name="propertyType">属性类型</param>
        /// <param name="value">属性值</param>
        /// <returns>属性值大小（字节）</returns>
        private static int GetPropertyValueSize(MqttPropertyType propertyType, object value)
        {
            return propertyType switch
            {
                MqttPropertyType.PayloadFormatIndicator => 1,
                MqttPropertyType.MessageExpiryInterval => 4,
                MqttPropertyType.ContentType => MqttPacketBase.GetStringLength(value as string),
                MqttPropertyType.ResponseTopic => MqttPacketBase.GetStringLength(value as string),
                MqttPropertyType.CorrelationData => MqttPacketBase.GetBinaryDataLength(value as byte[]),
                MqttPropertyType.SubscriptionIdentifier => MqttPacketBase.GetVariableByteIntegerLength((int)value),
                MqttPropertyType.SessionExpiryInterval => 4,
                MqttPropertyType.AssignedClientIdentifier => MqttPacketBase.GetStringLength(value as string),
                MqttPropertyType.ServerKeepAlive => 2,
                MqttPropertyType.AuthenticationMethod => MqttPacketBase.GetStringLength(value as string),
                MqttPropertyType.AuthenticationData => MqttPacketBase.GetBinaryDataLength(value as byte[]),
                MqttPropertyType.RequestProblemInformation => 1,
                MqttPropertyType.WillDelayInterval => 4,
                MqttPropertyType.RequestResponseInformation => 1,
                MqttPropertyType.ResponseInformation => MqttPacketBase.GetStringLength(value as string),
                MqttPropertyType.ServerReference => MqttPacketBase.GetStringLength(value as string),
                MqttPropertyType.ReasonString => MqttPacketBase.GetStringLength(value as string),
                MqttPropertyType.ReceiveMaximum => 2,
                MqttPropertyType.TopicAliasMaximum => 2,
                MqttPropertyType.TopicAlias => 2,
                MqttPropertyType.MaximumQoS => 1,
                MqttPropertyType.RetainAvailable => 1,
                MqttPropertyType.UserProperty => GetUserPropertyLength((ValueTuple<string, string>)value),
                MqttPropertyType.MaximumPacketSize => 4,
                MqttPropertyType.WildcardSubscriptionAvailable => 1,
                MqttPropertyType.SubscriptionIdentifierAvailable => 1,
                MqttPropertyType.SharedSubscriptionAvailable => 1,
                _ => 0
            };
        }

        /// <summary>
        /// 获取用户属性的长度
        /// </summary>
        /// <param name="userProperty">用户属性</param>
        /// <returns>属性长度</returns>
        private static int GetUserPropertyLength(ValueTuple<string, string> userProperty)
        {
            return MqttPacketBase.GetStringLength(userProperty.Item1) + MqttPacketBase.GetStringLength(userProperty.Item2);
        }
    }

    /// <summary>
    /// MQTT 5.0 属性类型
    /// </summary>
    public enum MqttPropertyType : byte
    {
        PayloadFormatIndicator = 0x01,
        MessageExpiryInterval = 0x02,
        ContentType = 0x03,
        ResponseTopic = 0x08,
        CorrelationData = 0x09,
        SubscriptionIdentifier = 0x0B,
        SessionExpiryInterval = 0x11,
        AssignedClientIdentifier = 0x12,
        ServerKeepAlive = 0x13,
        AuthenticationMethod = 0x15,
        AuthenticationData = 0x16,
        RequestProblemInformation = 0x17,
        WillDelayInterval = 0x18,
        RequestResponseInformation = 0x19,
        ResponseInformation = 0x1A,
        ServerReference = 0x1C,
        ReasonString = 0x1F,
        ReceiveMaximum = 0x21,
        TopicAliasMaximum = 0x22,
        TopicAlias = 0x23,
        MaximumQoS = 0x24,
        RetainAvailable = 0x25,
        UserProperty = 0x26,
        MaximumPacketSize = 0x27,
        WildcardSubscriptionAvailable = 0x28,
        SubscriptionIdentifierAvailable = 0x29,
        SharedSubscriptionAvailable = 0x2A
    }
}
