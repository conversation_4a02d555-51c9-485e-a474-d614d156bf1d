using Microsoft.Extensions.DependencyInjection;

namespace MqttBroker.Network;

/// <summary>
/// MQTT Broker 网络服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 MQTT Broker 网络服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttBrokerNetwork(this IServiceCollection services)
    {
        // TODO: 注册网络服务
        // 这里将在后续开发中添加具体的服务注册
        
        return services;
    }
}
