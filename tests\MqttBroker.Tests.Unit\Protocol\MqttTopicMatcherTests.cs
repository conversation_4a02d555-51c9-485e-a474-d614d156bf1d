using System.Linq;
using Xunit;
using MqttBroker.Core.Protocol;

namespace MqttBroker.Tests.Unit.Protocol
{
    /// <summary>
    /// MQTT 主题匹配器单元测试
    /// </summary>
    public class MqttTopicMatcherTests
    {
        private readonly MqttTopicMatcher _matcher;

        public MqttTopicMatcherTests()
        {
            _matcher = new MqttTopicMatcher();
        }

        [Theory]
        [InlineData("test/topic", "test/topic", true)]
        [InlineData("test/topic", "test/different", false)]
        [InlineData("test/+", "test/topic", true)]
        [InlineData("test/+", "test/topic/subtopic", false)]
        [InlineData("test/+/subtopic", "test/topic/subtopic", true)]
        [InlineData("test/#", "test/topic", true)]
        [InlineData("test/#", "test/topic/subtopic", true)]
        [InlineData("test/#", "test/topic/subtopic/deep", true)]
        [InlineData("test/#", "different/topic", false)]
        [InlineData("+", "topic", true)]
        [InlineData("+", "topic/subtopic", false)]
        [InlineData("#", "any/topic/structure", true)]
        [InlineData("sport/tennis/player1", "sport/tennis/player1", true)]
        [InlineData("sport/tennis/player1/ranking", "sport/tennis/player1/ranking", true)]
        [InlineData("sport/tennis/player1", "sport/tennis/player2", false)]
        [InlineData("sport/tennis/+", "sport/tennis/player1", true)]
        [InlineData("sport/tennis/+", "sport/tennis/player2", true)]
        [InlineData("sport/tennis/+", "sport/tennis/player1/ranking", false)]
        [InlineData("sport/+", "sport/tennis", true)]
        [InlineData("sport/+", "sport", false)]
        [InlineData("sport/+/player1", "sport/tennis/player1", true)]
        [InlineData("sport/+/player1", "sport/tennis/player2", false)]
        [InlineData("sport/#", "sport", true)]
        [InlineData("sport/#", "sport/tennis", true)]
        [InlineData("sport/#", "sport/tennis/player1", true)]
        [InlineData("sport/#", "sport/tennis/player1/ranking", true)]
        [InlineData("#", "sport/tennis/player1", true)]
        [InlineData("#", "", true)]
        [InlineData("sport/tennis/#", "sport/tennis", true)]
        [InlineData("sport/tennis/#", "sport/tennis/", true)]
        public void IsMatch_VariousTopicFiltersAndNames_ReturnsExpectedResult(
            string topicFilter, string topicName, bool expected)
        {
            // Act
            var result = _matcher.IsMatch(topicFilter, topicName);

            // Assert
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("$SYS/broker/uptime", "$SYS/broker/uptime", true)]
        [InlineData("$SYS/broker/uptime", "$SYS/broker/clients", false)]
        [InlineData("$SYS/+", "$SYS/broker/uptime", false)] // 系统主题不能使用通配符
        [InlineData("$SYS/#", "$SYS/broker/uptime", false)] // 系统主题不能使用通配符
        [InlineData("+/broker/uptime", "$SYS/broker/uptime", false)] // 系统主题不能使用通配符
        [InlineData("#", "$SYS/broker/uptime", false)] // 系统主题不能使用通配符
        public void IsMatch_SystemTopics_ReturnsExpectedResult(
            string topicFilter, string topicName, bool expected)
        {
            // Act
            var result = _matcher.IsMatch(topicFilter, topicName);

            // Assert
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData(null, "test/topic", false)]
        [InlineData("test/topic", null, false)]
        [InlineData("", "test/topic", false)]
        [InlineData("test/topic", "", false)]
        [InlineData(null, null, false)]
        public void IsMatch_NullOrEmptyInputs_ReturnsFalse(
            string topicFilter, string topicName, bool expected)
        {
            // Act
            var result = _matcher.IsMatch(topicFilter, topicName);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void GetMatchingFilters_MultipleFilters_ReturnsCorrectMatches()
        {
            // Arrange
            var topicFilters = new[]
            {
                "sensors/+/temperature",
                "sensors/+/humidity",
                "alerts/#",
                "system/status",
                "logs/+/error"
            };
            var topicName = "sensors/room1/temperature";

            // Act
            var matches = _matcher.GetMatchingFilters(topicFilters, topicName).ToList();

            // Assert
            Assert.Single(matches);
            Assert.Contains("sensors/+/temperature", matches);
        }

        [Fact]
        public void GetMatchingFilters_NoMatches_ReturnsEmpty()
        {
            // Arrange
            var topicFilters = new[]
            {
                "sensors/+/humidity",
                "alerts/#",
                "system/status"
            };
            var topicName = "sensors/room1/temperature";

            // Act
            var matches = _matcher.GetMatchingFilters(topicFilters, topicName).ToList();

            // Assert
            Assert.Empty(matches);
        }

        [Fact]
        public void GetMatchingFilters_MultipleMatches_ReturnsAllMatches()
        {
            // Arrange
            var topicFilters = new[]
            {
                "sensors/+/temperature",
                "sensors/#",
                "+/room1/temperature",
                "#"
            };
            var topicName = "sensors/room1/temperature";

            // Act
            var matches = _matcher.GetMatchingFilters(topicFilters, topicName).ToList();

            // Assert
            Assert.Equal(4, matches.Count);
            Assert.Contains("sensors/+/temperature", matches);
            Assert.Contains("sensors/#", matches);
            Assert.Contains("+/room1/temperature", matches);
            Assert.Contains("#", matches);
        }

        [Theory]
        [InlineData("test/topic", true)]
        [InlineData("test/+", true)]
        [InlineData("test/#", true)]
        [InlineData("test/+/subtopic", true)]
        [InlineData("test/topic/+", true)]
        [InlineData("+", true)]
        [InlineData("#", true)]
        [InlineData("test/topic#", false)] // # 必须单独占据一个级别
        [InlineData("test/+topic", false)] // + 必须单独占据一个级别
        [InlineData("test/topic/+/", true)]
        [InlineData("test/topic/#/subtopic", false)] // # 必须是最后一个级别
        [InlineData("test/topic\0", false)] // 不能包含空字符
        [InlineData("", false)]
        [InlineData(null, false)]
        public void IsValidTopicFilter_VariousFilters_ReturnsExpectedResult(
            string topicFilter, bool expected)
        {
            // Act
            var result = _matcher.IsValidTopicFilter(topicFilter);

            // Assert
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("test/topic", true)]
        [InlineData("test/topic/subtopic", true)]
        [InlineData("$SYS/broker/uptime", true)]
        [InlineData("test/+", false)] // 主题名称不能包含通配符
        [InlineData("test/#", false)] // 主题名称不能包含通配符
        [InlineData("test/topic\0", false)] // 不能包含空字符
        [InlineData("", false)]
        [InlineData(null, false)]
        public void IsValidTopicName_VariousNames_ReturnsExpectedResult(
            string topicName, bool expected)
        {
            // Act
            var result = _matcher.IsValidTopicName(topicName);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void IsValidTopicFilter_TooLongFilter_ReturnsFalse()
        {
            // Arrange
            var longFilter = new string('a', MqttProtocolConstants.PacketSizeLimits.MaxTopicLength + 1);

            // Act
            var result = _matcher.IsValidTopicFilter(longFilter);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsValidTopicName_TooLongName_ReturnsFalse()
        {
            // Arrange
            var longName = new string('a', MqttProtocolConstants.PacketSizeLimits.MaxTopicLength + 1);

            // Act
            var result = _matcher.IsValidTopicName(longName);

            // Assert
            Assert.False(result);
        }

        [Theory]
        [InlineData("a/b", "a/b", true)]
        [InlineData("a/b/c", "a/b/c", true)]
        [InlineData("a/b", "a/b/c", false)]
        [InlineData("a/b/c", "a/b", false)]
        [InlineData("", "", true)]
        [InlineData("a", "", false)]
        [InlineData("", "a", false)]
        public void IsMatch_EdgeCases_ReturnsExpectedResult(
            string topicFilter, string topicName, bool expected)
        {
            // Act
            var result = _matcher.IsMatch(topicFilter, topicName);

            // Assert
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("sport/tennis/+", "sport/tennis/", true)] // 空级别匹配
        [InlineData("sport/tennis/+", "sport/tennis", false)] // 缺少级别
        [InlineData("sport/+/player1", "sport//player1", true)] // 空级别匹配
        [InlineData("sport/#", "sport/", true)] // 空级别匹配
        [InlineData("+/+", "a/b", true)]
        [InlineData("+/+", "a", false)]
        [InlineData("+/+", "a/b/c", false)]
        public void IsMatch_EmptyLevels_ReturnsExpectedResult(
            string topicFilter, string topicName, bool expected)
        {
            // Act
            var result = _matcher.IsMatch(topicFilter, topicName);

            // Assert
            Assert.Equal(expected, result);
        }
    }
}
