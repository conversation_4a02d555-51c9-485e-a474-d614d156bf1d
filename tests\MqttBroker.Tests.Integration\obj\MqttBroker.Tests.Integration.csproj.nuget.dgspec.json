{"format": 1, "restore": {"D:\\Project\\02 Broker\\tests\\MqttBroker.Tests.Integration\\MqttBroker.Tests.Integration.csproj": {}}, "projects": {"D:\\Project\\02 Broker\\src\\MqttBroker.Configuration\\MqttBroker.Configuration.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\02 Broker\\src\\MqttBroker.Configuration\\MqttBroker.Configuration.csproj", "projectName": "MqttBroker.Configuration", "projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Configuration\\MqttBroker.Configuration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Configuration\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\02 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\02 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj", "projectName": "MqttBroker.Core", "projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "System.Buffers": {"target": "Package", "version": "[4.5.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\02 Broker\\src\\MqttBroker.Host\\MqttBroker.Host.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\02 Broker\\src\\MqttBroker.Host\\MqttBroker.Host.csproj", "projectName": "MqttBroker.Host", "projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Host\\MqttBroker.Host.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Host\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Project\\02 Broker\\src\\MqttBroker.Configuration\\MqttBroker.Configuration.csproj": {"projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Configuration\\MqttBroker.Configuration.csproj"}, "D:\\Project\\02 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj": {"projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj"}, "D:\\Project\\02 Broker\\src\\MqttBroker.Logging\\MqttBroker.Logging.csproj": {"projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Logging\\MqttBroker.Logging.csproj"}, "D:\\Project\\02 Broker\\src\\MqttBroker.Metrics\\MqttBroker.Metrics.csproj": {"projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Metrics\\MqttBroker.Metrics.csproj"}, "D:\\Project\\02 Broker\\src\\MqttBroker.Network\\MqttBroker.Network.csproj": {"projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Network\\MqttBroker.Network.csproj"}, "D:\\Project\\02 Broker\\src\\MqttBroker.Storage\\MqttBroker.Storage.csproj": {"projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Storage\\MqttBroker.Storage.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\02 Broker\\src\\MqttBroker.Logging\\MqttBroker.Logging.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\02 Broker\\src\\MqttBroker.Logging\\MqttBroker.Logging.csproj", "projectName": "MqttBroker.Logging", "projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Logging\\MqttBroker.Logging.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Logging\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Serilog.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\02 Broker\\src\\MqttBroker.Metrics\\MqttBroker.Metrics.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\02 Broker\\src\\MqttBroker.Metrics\\MqttBroker.Metrics.csproj", "projectName": "MqttBroker.Metrics", "projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Metrics\\MqttBroker.Metrics.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Metrics\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\02 Broker\\src\\MqttBroker.Network\\MqttBroker.Network.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\02 Broker\\src\\MqttBroker.Network\\MqttBroker.Network.csproj", "projectName": "MqttBroker.Network", "projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Network\\MqttBroker.Network.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Network\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Project\\02 Broker\\src\\MqttBroker.Configuration\\MqttBroker.Configuration.csproj": {"projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Configuration\\MqttBroker.Configuration.csproj"}, "D:\\Project\\02 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj": {"projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj"}, "D:\\Project\\02 Broker\\src\\MqttBroker.Logging\\MqttBroker.Logging.csproj": {"projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Logging\\MqttBroker.Logging.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[9.0.6, )"}, "System.IO.Pipelines": {"target": "Package", "version": "[9.0.6, )"}, "System.Threading.Channels": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\02 Broker\\src\\MqttBroker.Storage\\MqttBroker.Storage.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\02 Broker\\src\\MqttBroker.Storage\\MqttBroker.Storage.csproj", "projectName": "MqttBroker.Storage", "projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Storage\\MqttBroker.Storage.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Storage\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Project\\02 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj": {"projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\02 Broker\\tests\\MqttBroker.Tests.Integration\\MqttBroker.Tests.Integration.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\02 Broker\\tests\\MqttBroker.Tests.Integration\\MqttBroker.Tests.Integration.csproj", "projectName": "MqttBroker.Tests.Integration", "projectPath": "D:\\Project\\02 Broker\\tests\\MqttBroker.Tests.Integration\\MqttBroker.Tests.Integration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\02 Broker\\tests\\MqttBroker.Tests.Integration\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Project\\02 Broker\\src\\MqttBroker.Host\\MqttBroker.Host.csproj": {"projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Host\\MqttBroker.Host.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.0, )"}, "xunit": {"target": "Package", "version": "[2.5.3, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}