using System;
using System.Buffers;
using System.Text;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Tests.Unit.Protocol
{
    /// <summary>
    /// MQTT 数据包解析器单元测试
    /// </summary>
    public class MqttPacketParserTests
    {
        private readonly Mock<ILogger<MqttPacketParser>> _loggerMock;
        private readonly MqttPacketParser _parser;

        public MqttPacketParserTests()
        {
            _loggerMock = new Mock<ILogger<MqttPacketParser>>();
            _parser = new MqttPacketParser(_loggerMock.Object);
        }

        [Fact]
        public void TryParsePacketHeader_ValidHeader_ReturnsTrue()
        {
            // Arrange
            var buffer = new byte[] { 0x10, 0x0A }; // CONNECT packet with remaining length 10
            var sequence = new ReadOnlySequence<byte>(buffer);

            // Act
            var result = _parser.TryParsePacketHeader(sequence, out var packetLength, out var headerLength);

            // Assert
            Assert.True(result);
            Assert.Equal(12, packetLength); // 2 (header) + 10 (remaining)
            Assert.Equal(2, headerLength);
        }

        [Fact]
        public void TryParsePacketHeader_InsufficientData_ReturnsFalse()
        {
            // Arrange
            var buffer = new byte[] { 0x10 }; // Only packet type, no remaining length
            var sequence = new ReadOnlySequence<byte>(buffer);

            // Act
            var result = _parser.TryParsePacketHeader(sequence, out var packetLength, out var headerLength);

            // Assert
            Assert.False(result);
            Assert.Equal(0, packetLength);
            Assert.Equal(0, headerLength);
        }

        [Fact]
        public void TryParsePacketHeader_VariableLengthInteger_ReturnsCorrectLength()
        {
            // Arrange - remaining length 16383 (0xFF 0x7F)
            var buffer = new byte[] { 0x10, 0xFF, 0x7F };
            var sequence = new ReadOnlySequence<byte>(buffer);

            // Act
            var result = _parser.TryParsePacketHeader(sequence, out var packetLength, out var headerLength);

            // Assert
            Assert.True(result);
            Assert.Equal(16386, packetLength); // 3 (header) + 16383 (remaining)
            Assert.Equal(3, headerLength);
        }

        [Fact]
        public void HasCompletePacket_CompletePacket_ReturnsTrue()
        {
            // Arrange
            var buffer = new byte[] { 0x10, 0x02, 0x00, 0x04 }; // CONNECT with 2 bytes remaining
            var sequence = new ReadOnlySequence<byte>(buffer);

            // Act
            var result = _parser.HasCompletePacket(sequence);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void HasCompletePacket_IncompletePacket_ReturnsFalse()
        {
            // Arrange
            var buffer = new byte[] { 0x10, 0x05, 0x00, 0x04 }; // CONNECT with 5 bytes remaining but only 2 provided
            var sequence = new ReadOnlySequence<byte>(buffer);

            // Act
            var result = _parser.HasCompletePacket(sequence);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ParsePacket_PingReqPacket_ReturnsCorrectPacket()
        {
            // Arrange
            var buffer = new byte[] { 0xC0, 0x00 }; // PINGREQ packet
            var sequence = new ReadOnlySequence<byte>(buffer);

            // Act
            var packet = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // Assert
            Assert.NotNull(packet);
            Assert.IsType<MqttPingReqPacket>(packet);
            Assert.Equal(MqttPacketType.PingReq, packet.PacketType);
        }

        [Fact]
        public void ParsePacket_PingRespPacket_ReturnsCorrectPacket()
        {
            // Arrange
            var buffer = new byte[] { 0xD0, 0x00 }; // PINGRESP packet
            var sequence = new ReadOnlySequence<byte>(buffer);

            // Act
            var packet = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // Assert
            Assert.NotNull(packet);
            Assert.IsType<MqttPingRespPacket>(packet);
            Assert.Equal(MqttPacketType.PingResp, packet.PacketType);
        }

        [Fact]
        public void ParsePacket_PublishPacketQoS0_ReturnsCorrectPacket()
        {
            // Arrange
            var topic = "test/topic";
            var payload = "Hello World";
            var topicBytes = Encoding.UTF8.GetBytes(topic);
            var payloadBytes = Encoding.UTF8.GetBytes(payload);
            
            var buffer = new byte[4 + topicBytes.Length + payloadBytes.Length];
            buffer[0] = 0x30; // PUBLISH QoS 0
            buffer[1] = (byte)(2 + topicBytes.Length + payloadBytes.Length); // Remaining length
            buffer[2] = (byte)(topicBytes.Length >> 8); // Topic length MSB
            buffer[3] = (byte)(topicBytes.Length & 0xFF); // Topic length LSB
            
            Array.Copy(topicBytes, 0, buffer, 4, topicBytes.Length);
            Array.Copy(payloadBytes, 0, buffer, 4 + topicBytes.Length, payloadBytes.Length);
            
            var sequence = new ReadOnlySequence<byte>(buffer);

            // Act
            var packet = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // Assert
            Assert.NotNull(packet);
            Assert.IsType<MqttPublishPacket>(packet);
            var publishPacket = (MqttPublishPacket)packet;
            Assert.Equal(topic, publishPacket.Topic);
            Assert.Equal(payload, publishPacket.GetPayloadAsString());
            Assert.Equal(MqttQoSLevel.AtMostOnce, publishPacket.QoSLevel);
            Assert.Null(publishPacket.PacketIdentifier);
        }

        [Fact]
        public void ParsePacket_PublishPacketQoS1_ReturnsCorrectPacket()
        {
            // Arrange
            var topic = "test/topic";
            var payload = "Hello World";
            var packetId = (ushort)1234;
            var topicBytes = Encoding.UTF8.GetBytes(topic);
            var payloadBytes = Encoding.UTF8.GetBytes(payload);
            
            var buffer = new byte[6 + topicBytes.Length + payloadBytes.Length];
            buffer[0] = 0x32; // PUBLISH QoS 1
            buffer[1] = (byte)(4 + topicBytes.Length + payloadBytes.Length); // Remaining length
            buffer[2] = (byte)(topicBytes.Length >> 8); // Topic length MSB
            buffer[3] = (byte)(topicBytes.Length & 0xFF); // Topic length LSB
            
            Array.Copy(topicBytes, 0, buffer, 4, topicBytes.Length);
            
            var packetIdOffset = 4 + topicBytes.Length;
            buffer[packetIdOffset] = (byte)(packetId >> 8); // Packet ID MSB
            buffer[packetIdOffset + 1] = (byte)(packetId & 0xFF); // Packet ID LSB
            
            Array.Copy(payloadBytes, 0, buffer, packetIdOffset + 2, payloadBytes.Length);
            
            var sequence = new ReadOnlySequence<byte>(buffer);

            // Act
            var packet = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // Assert
            Assert.NotNull(packet);
            Assert.IsType<MqttPublishPacket>(packet);
            var publishPacket = (MqttPublishPacket)packet;
            Assert.Equal(topic, publishPacket.Topic);
            Assert.Equal(payload, publishPacket.GetPayloadAsString());
            Assert.Equal(MqttQoSLevel.AtLeastOnce, publishPacket.QoSLevel);
            Assert.Equal(packetId, publishPacket.PacketIdentifier);
        }

        [Fact]
        public void ParsePacket_PubAckPacket_ReturnsCorrectPacket()
        {
            // Arrange
            var packetId = (ushort)5678;
            var buffer = new byte[]
            {
                0x40, // PUBACK
                0x02, // Remaining length
                (byte)(packetId >> 8), // Packet ID MSB
                (byte)(packetId & 0xFF) // Packet ID LSB
            };
            var sequence = new ReadOnlySequence<byte>(buffer);

            // Act
            var packet = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // Assert
            Assert.NotNull(packet);
            Assert.IsType<MqttPubAckPacket>(packet);
            var pubAckPacket = (MqttPubAckPacket)packet;
            Assert.Equal(packetId, pubAckPacket.PacketIdentifier);
        }

        [Fact]
        public void ParsePacket_InvalidPacketType_ReturnsNull()
        {
            // Arrange
            var buffer = new byte[] { 0x00, 0x00 }; // Invalid packet type (0)
            var sequence = new ReadOnlySequence<byte>(buffer);

            // Act
            var packet = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // Assert
            Assert.Null(packet);
        }

        [Fact]
        public void ParsePacket_MalformedData_ReturnsNull()
        {
            // Arrange
            var buffer = new byte[] { 0x10, 0x05, 0x00 }; // CONNECT with insufficient data
            var sequence = new ReadOnlySequence<byte>(buffer);

            // Act
            var packet = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // Assert
            Assert.Null(packet);
        }

        [Theory]
        [InlineData(new byte[] { 0x10 }, false)] // Only packet type
        [InlineData(new byte[] { 0x10, 0x02 }, false)] // Header but incomplete payload
        [InlineData(new byte[] { 0x10, 0x02, 0x00, 0x04 }, true)] // Complete packet
        [InlineData(new byte[] { 0xC0, 0x00 }, true)] // PINGREQ (no payload)
        public void HasCompletePacket_VariousBuffers_ReturnsExpectedResult(byte[] buffer, bool expected)
        {
            // Arrange
            var sequence = new ReadOnlySequence<byte>(buffer);

            // Act
            var result = _parser.HasCompletePacket(sequence);

            // Assert
            Assert.Equal(expected, result);
        }
    }
}
