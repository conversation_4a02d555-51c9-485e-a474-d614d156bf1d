using System;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Protocol
{
    /// <summary>
    /// MQTT 数据包验证器实现
    /// </summary>
    public class MqttPacketValidator : IMqttPacketValidator
    {
        private readonly ILogger<MqttPacketValidator> _logger;

        /// <summary>
        /// 初始化 MQTT 数据包验证器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public MqttPacketValidator(ILogger<MqttPacketValidator> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <param name="packet">要验证的数据包</param>
        /// <param name="protocolVersion">MQTT 协议版本</param>
        /// <returns>验证结果</returns>
        public MqttPacketValidationResult ValidatePacket(IMqttPacket packet, MqttProtocolVersion protocolVersion)
        {
            if (packet == null)
            {
                return MqttPacketValidationResult.Failure("Packet cannot be null", MqttReasonCode.MalformedPacket);
            }

            try
            {
                _logger.LogDebug("Validating packet: Type={PacketType}, ProtocolVersion={ProtocolVersion}", 
                    packet.PacketType, protocolVersion);

                // 基本验证
                if (!packet.IsValid())
                {
                    return MqttPacketValidationResult.Failure($"Packet {packet.PacketType} failed basic validation", 
                        MqttReasonCode.MalformedPacket);
                }

                // 根据数据包类型进行特定验证
                var result = packet switch
                {
                    MqttConnectPacket connectPacket => ValidateConnectPacket(connectPacket, protocolVersion),
                    MqttConnAckPacket connAckPacket => ValidateConnAckPacket(connAckPacket, protocolVersion),
                    MqttPublishPacket publishPacket => ValidatePublishPacket(publishPacket, protocolVersion),
                    MqttPubAckPacket pubAckPacket => ValidatePubAckPacket(pubAckPacket, protocolVersion),
                    MqttPubRecPacket pubRecPacket => ValidatePubRecPacket(pubRecPacket, protocolVersion),
                    MqttPubRelPacket pubRelPacket => ValidatePubRelPacket(pubRelPacket, protocolVersion),
                    MqttPubCompPacket pubCompPacket => ValidatePubCompPacket(pubCompPacket, protocolVersion),
                    MqttSubscribePacket subscribePacket => ValidateSubscribePacket(subscribePacket, protocolVersion),
                    MqttSubAckPacket subAckPacket => ValidateSubAckPacket(subAckPacket, protocolVersion),
                    MqttUnsubscribePacket unsubscribePacket => ValidateUnsubscribePacket(unsubscribePacket, protocolVersion),
                    MqttUnsubAckPacket unsubAckPacket => ValidateUnsubAckPacket(unsubAckPacket, protocolVersion),
                    MqttPingReqPacket pingReqPacket => ValidatePingReqPacket(pingReqPacket),
                    MqttPingRespPacket pingRespPacket => ValidatePingRespPacket(pingRespPacket),
                    MqttDisconnectPacket disconnectPacket => ValidateDisconnectPacket(disconnectPacket, protocolVersion),
                    MqttAuthPacket authPacket => ValidateAuthPacket(authPacket, protocolVersion),
                    _ => MqttPacketValidationResult.Failure($"Unknown packet type: {packet.GetType().Name}", 
                        MqttReasonCode.MalformedPacket)
                };

                if (result.IsValid)
                {
                    _logger.LogDebug("Packet validation successful: Type={PacketType}", packet.PacketType);
                }
                else
                {
                    _logger.LogWarning("Packet validation failed: Type={PacketType}, Error={Error}", 
                        packet.PacketType, result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating packet: Type={PacketType}", packet.PacketType);
                return MqttPacketValidationResult.Failure($"Validation error: {ex.Message}", 
                    MqttReasonCode.ImplementationSpecificError);
            }
        }

        #region 私有验证方法

        private MqttPacketValidationResult ValidateConnectPacket(MqttConnectPacket packet, MqttProtocolVersion protocolVersion)
        {
            // 验证协议名称
            var expectedProtocolName = protocolVersion switch
            {
                MqttProtocolVersion.Version311 => MqttProtocolConstants.ProtocolNames.Mqtt311,
                MqttProtocolVersion.Version50 => MqttProtocolConstants.ProtocolNames.Mqtt50,
                _ => null
            };

            if (packet.ProtocolName != expectedProtocolName)
            {
                return MqttPacketValidationResult.Failure($"Invalid protocol name: {packet.ProtocolName}", 
                    MqttReasonCode.UnsupportedProtocolVersion);
            }

            // 验证协议版本
            if (packet.ProtocolVersion != protocolVersion)
            {
                return MqttPacketValidationResult.Failure($"Protocol version mismatch: expected {protocolVersion}, got {packet.ProtocolVersion}", 
                    MqttReasonCode.UnsupportedProtocolVersion);
            }

            // 验证客户端ID
            if (string.IsNullOrEmpty(packet.ClientId) && !packet.CleanSession)
            {
                return MqttPacketValidationResult.Failure("Client ID cannot be empty when CleanSession is false", 
                    MqttReasonCode.ClientIdentifierNotValid);
            }

            // 验证遗嘱消息
            if (packet.WillMessage != null)
            {
                if (string.IsNullOrEmpty(packet.WillMessage.Topic))
                {
                    return MqttPacketValidationResult.Failure("Will topic cannot be empty", 
                        MqttReasonCode.TopicNameInvalid);
                }

                if (packet.WillMessage.Topic.Contains('\0'))
                {
                    return MqttPacketValidationResult.Failure("Will topic cannot contain null characters", 
                        MqttReasonCode.TopicNameInvalid);
                }
            }

            // 验证用户名和密码
            if (!string.IsNullOrEmpty(packet.Password) && string.IsNullOrEmpty(packet.Username))
            {
                return MqttPacketValidationResult.Failure("Password cannot be set without username", 
                    MqttReasonCode.BadUserNameOrPassword);
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidateConnAckPacket(MqttConnAckPacket packet, MqttProtocolVersion protocolVersion)
        {
            // CONNACK 验证相对简单，主要检查原因码的有效性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                // 验证 MQTT 5.0 原因码
                if (!IsValidConnAckReasonCode(packet.ReasonCode))
                {
                    return MqttPacketValidationResult.Failure($"Invalid CONNACK reason code: {packet.ReasonCode}", 
                        MqttReasonCode.ProtocolError);
                }
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidatePublishPacket(MqttPublishPacket packet, MqttProtocolVersion protocolVersion)
        {
            // 验证主题名称
            if (string.IsNullOrEmpty(packet.Topic))
            {
                return MqttPacketValidationResult.Failure("Topic cannot be empty", 
                    MqttReasonCode.TopicNameInvalid);
            }

            if (packet.Topic.Contains('\0'))
            {
                return MqttPacketValidationResult.Failure("Topic cannot contain null characters", 
                    MqttReasonCode.TopicNameInvalid);
            }

            // 主题名称不能包含通配符
            if (packet.Topic.Contains('+') || packet.Topic.Contains('#'))
            {
                return MqttPacketValidationResult.Failure("Topic name cannot contain wildcards", 
                    MqttReasonCode.TopicNameInvalid);
            }

            // 验证 QoS 和数据包标识符
            if (packet.QoSLevel > MqttQoSLevel.AtMostOnce)
            {
                if (!packet.PacketIdentifier.HasValue || packet.PacketIdentifier.Value == 0)
                {
                    return MqttPacketValidationResult.Failure("Packet identifier is required for QoS > 0", 
                        MqttReasonCode.ProtocolError);
                }
            }
            else
            {
                if (packet.PacketIdentifier.HasValue)
                {
                    return MqttPacketValidationResult.Failure("Packet identifier must not be present for QoS 0", 
                        MqttReasonCode.ProtocolError);
                }
            }

            // 验证载荷大小
            if (packet.Payload != null && packet.Payload.Length > MqttProtocolConstants.PacketSizeLimits.MaxPacketSize)
            {
                return MqttPacketValidationResult.Failure("Payload is too large", 
                    MqttReasonCode.PacketTooLarge);
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidatePubAckPacket(MqttPubAckPacket packet, MqttProtocolVersion protocolVersion)
        {
            if (!packet.PacketIdentifier.HasValue || packet.PacketIdentifier.Value == 0)
            {
                return MqttPacketValidationResult.Failure("PUBACK must have a valid packet identifier", 
                    MqttReasonCode.ProtocolError);
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidatePubRecPacket(MqttPubRecPacket packet, MqttProtocolVersion protocolVersion)
        {
            if (!packet.PacketIdentifier.HasValue || packet.PacketIdentifier.Value == 0)
            {
                return MqttPacketValidationResult.Failure("PUBREC must have a valid packet identifier", 
                    MqttReasonCode.ProtocolError);
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidatePubRelPacket(MqttPubRelPacket packet, MqttProtocolVersion protocolVersion)
        {
            if (!packet.PacketIdentifier.HasValue || packet.PacketIdentifier.Value == 0)
            {
                return MqttPacketValidationResult.Failure("PUBREL must have a valid packet identifier", 
                    MqttReasonCode.ProtocolError);
            }

            // PUBREL 的标志位必须是 0x02
            if (packet.Flags != 0x02)
            {
                return MqttPacketValidationResult.Failure("PUBREL flags must be 0x02", 
                    MqttReasonCode.MalformedPacket);
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidatePubCompPacket(MqttPubCompPacket packet, MqttProtocolVersion protocolVersion)
        {
            if (!packet.PacketIdentifier.HasValue || packet.PacketIdentifier.Value == 0)
            {
                return MqttPacketValidationResult.Failure("PUBCOMP must have a valid packet identifier", 
                    MqttReasonCode.ProtocolError);
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidateSubscribePacket(MqttSubscribePacket packet, MqttProtocolVersion protocolVersion)
        {
            if (!packet.PacketIdentifier.HasValue || packet.PacketIdentifier.Value == 0)
            {
                return MqttPacketValidationResult.Failure("SUBSCRIBE must have a valid packet identifier", 
                    MqttReasonCode.ProtocolError);
            }

            // SUBSCRIBE 的标志位必须是 0x02
            if (packet.Flags != 0x02)
            {
                return MqttPacketValidationResult.Failure("SUBSCRIBE flags must be 0x02", 
                    MqttReasonCode.MalformedPacket);
            }

            if (packet.Subscriptions == null || packet.Subscriptions.Count == 0)
            {
                return MqttPacketValidationResult.Failure("SUBSCRIBE must contain at least one subscription", 
                    MqttReasonCode.ProtocolError);
            }

            // 验证每个订阅
            foreach (var subscription in packet.Subscriptions)
            {
                if (string.IsNullOrEmpty(subscription.TopicFilter))
                {
                    return MqttPacketValidationResult.Failure("Topic filter cannot be empty", 
                        MqttReasonCode.TopicFilterInvalid);
                }

                if (!IsValidTopicFilter(subscription.TopicFilter))
                {
                    return MqttPacketValidationResult.Failure($"Invalid topic filter: {subscription.TopicFilter}", 
                        MqttReasonCode.TopicFilterInvalid);
                }
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidateSubAckPacket(MqttSubAckPacket packet, MqttProtocolVersion protocolVersion)
        {
            if (!packet.PacketIdentifier.HasValue || packet.PacketIdentifier.Value == 0)
            {
                return MqttPacketValidationResult.Failure("SUBACK must have a valid packet identifier", 
                    MqttReasonCode.ProtocolError);
            }

            if (packet.ReasonCodes == null || packet.ReasonCodes.Count == 0)
            {
                return MqttPacketValidationResult.Failure("SUBACK must contain at least one reason code", 
                    MqttReasonCode.ProtocolError);
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidateUnsubscribePacket(MqttUnsubscribePacket packet, MqttProtocolVersion protocolVersion)
        {
            if (!packet.PacketIdentifier.HasValue || packet.PacketIdentifier.Value == 0)
            {
                return MqttPacketValidationResult.Failure("UNSUBSCRIBE must have a valid packet identifier", 
                    MqttReasonCode.ProtocolError);
            }

            // UNSUBSCRIBE 的标志位必须是 0x02
            if (packet.Flags != 0x02)
            {
                return MqttPacketValidationResult.Failure("UNSUBSCRIBE flags must be 0x02", 
                    MqttReasonCode.MalformedPacket);
            }

            if (packet.TopicFilters == null || packet.TopicFilters.Count == 0)
            {
                return MqttPacketValidationResult.Failure("UNSUBSCRIBE must contain at least one topic filter", 
                    MqttReasonCode.ProtocolError);
            }

            // 验证每个主题过滤器
            foreach (var topicFilter in packet.TopicFilters)
            {
                if (string.IsNullOrEmpty(topicFilter))
                {
                    return MqttPacketValidationResult.Failure("Topic filter cannot be empty", 
                        MqttReasonCode.TopicFilterInvalid);
                }

                if (!IsValidTopicFilter(topicFilter))
                {
                    return MqttPacketValidationResult.Failure($"Invalid topic filter: {topicFilter}", 
                        MqttReasonCode.TopicFilterInvalid);
                }
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidateUnsubAckPacket(MqttUnsubAckPacket packet, MqttProtocolVersion protocolVersion)
        {
            if (!packet.PacketIdentifier.HasValue || packet.PacketIdentifier.Value == 0)
            {
                return MqttPacketValidationResult.Failure("UNSUBACK must have a valid packet identifier", 
                    MqttReasonCode.ProtocolError);
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidatePingReqPacket(MqttPingReqPacket packet)
        {
            if (packet.PacketIdentifier.HasValue)
            {
                return MqttPacketValidationResult.Failure("PINGREQ must not have a packet identifier", 
                    MqttReasonCode.ProtocolError);
            }

            if (packet.Flags != 0)
            {
                return MqttPacketValidationResult.Failure("PINGREQ flags must be 0", 
                    MqttReasonCode.MalformedPacket);
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidatePingRespPacket(MqttPingRespPacket packet)
        {
            if (packet.PacketIdentifier.HasValue)
            {
                return MqttPacketValidationResult.Failure("PINGRESP must not have a packet identifier", 
                    MqttReasonCode.ProtocolError);
            }

            if (packet.Flags != 0)
            {
                return MqttPacketValidationResult.Failure("PINGRESP flags must be 0", 
                    MqttReasonCode.MalformedPacket);
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidateDisconnectPacket(MqttDisconnectPacket packet, MqttProtocolVersion protocolVersion)
        {
            if (packet.PacketIdentifier.HasValue)
            {
                return MqttPacketValidationResult.Failure("DISCONNECT must not have a packet identifier", 
                    MqttReasonCode.ProtocolError);
            }

            if (packet.Flags != 0)
            {
                return MqttPacketValidationResult.Failure("DISCONNECT flags must be 0", 
                    MqttReasonCode.MalformedPacket);
            }

            return MqttPacketValidationResult.Success();
        }

        private MqttPacketValidationResult ValidateAuthPacket(MqttAuthPacket packet, MqttProtocolVersion protocolVersion)
        {
            if (protocolVersion != MqttProtocolVersion.Version50)
            {
                return MqttPacketValidationResult.Failure("AUTH packet is only supported in MQTT 5.0", 
                    MqttReasonCode.UnsupportedProtocolVersion);
            }

            if (packet.PacketIdentifier.HasValue)
            {
                return MqttPacketValidationResult.Failure("AUTH must not have a packet identifier", 
                    MqttReasonCode.ProtocolError);
            }

            if (packet.Flags != 0)
            {
                return MqttPacketValidationResult.Failure("AUTH flags must be 0", 
                    MqttReasonCode.MalformedPacket);
            }

            // 验证原因码
            if (packet.ReasonCode != MqttReasonCode.Success &&
                packet.ReasonCode != MqttReasonCode.ContinueAuthentication &&
                packet.ReasonCode != MqttReasonCode.ReAuthenticate)
            {
                return MqttPacketValidationResult.Failure($"Invalid AUTH reason code: {packet.ReasonCode}", 
                    MqttReasonCode.ProtocolError);
            }

            return MqttPacketValidationResult.Success();
        }

        #endregion

        #region 辅助方法

        private static bool IsValidConnAckReasonCode(MqttReasonCode reasonCode)
        {
            return reasonCode switch
            {
                MqttReasonCode.Success => true,
                MqttReasonCode.UnspecifiedError => true,
                MqttReasonCode.MalformedPacket => true,
                MqttReasonCode.ProtocolError => true,
                MqttReasonCode.ImplementationSpecificError => true,
                MqttReasonCode.UnsupportedProtocolVersion => true,
                MqttReasonCode.ClientIdentifierNotValid => true,
                MqttReasonCode.BadUserNameOrPassword => true,
                MqttReasonCode.NotAuthorized => true,
                MqttReasonCode.ServerUnavailable => true,
                MqttReasonCode.ServerBusy => true,
                MqttReasonCode.Banned => true,
                MqttReasonCode.BadAuthenticationMethod => true,
                MqttReasonCode.TopicNameInvalid => true,
                MqttReasonCode.PacketTooLarge => true,
                MqttReasonCode.QuotaExceeded => true,
                MqttReasonCode.PayloadFormatInvalid => true,
                MqttReasonCode.RetainNotSupported => true,
                MqttReasonCode.QoSNotSupported => true,
                MqttReasonCode.UseAnotherServer => true,
                MqttReasonCode.ServerMoved => true,
                MqttReasonCode.ConnectionRateExceeded => true,
                _ => false
            };
        }

        private static bool IsValidTopicFilter(string topicFilter)
        {
            if (string.IsNullOrEmpty(topicFilter))
                return false;

            if (topicFilter.Contains('\0'))
                return false;

            // 验证通配符使用规则
            var parts = topicFilter.Split('/');
            
            for (int i = 0; i < parts.Length; i++)
            {
                var part = parts[i];
                
                // 检查单级通配符
                if (part.Contains('+'))
                {
                    // 单级通配符必须单独占据一个级别
                    if (part.Length != 1)
                        return false;
                }
                
                // 检查多级通配符
                if (part.Contains('#'))
                {
                    // 多级通配符必须单独占据一个级别，且必须是最后一个级别
                    if (part.Length != 1 || i != parts.Length - 1)
                        return false;
                }
            }

            return true;
        }

        #endregion
    }
}
