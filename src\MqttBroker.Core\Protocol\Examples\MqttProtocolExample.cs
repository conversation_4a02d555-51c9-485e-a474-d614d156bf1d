using System;
using System.Buffers;
using System.Text;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Protocol.Examples
{
    /// <summary>
    /// MQTT 协议解析器使用示例
    /// </summary>
    public class MqttProtocolExample
    {
        private readonly IMqttPacketParser _parser;
        private readonly IMqttPacketSerializer _serializer;
        private readonly IMqttPacketValidator _validator;
        private readonly IMqttTopicMatcher _topicMatcher;
        private readonly ILogger<MqttProtocolExample> _logger;

        /// <summary>
        /// 初始化示例
        /// </summary>
        public MqttProtocolExample(
            IMqttPacketParser parser,
            IMqttPacketSerializer serializer,
            IMqttPacketValidator validator,
            IMqttTopicMatcher topicMatcher,
            ILogger<MqttProtocolExample> logger)
        {
            _parser = parser ?? throw new ArgumentNullException(nameof(parser));
            _serializer = serializer ?? throw new ArgumentNullException(nameof(serializer));
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _topicMatcher = topicMatcher ?? throw new ArgumentNullException(nameof(topicMatcher));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 演示 CONNECT 数据包的创建、序列化和解析
        /// </summary>
        public void DemonstrateConnectPacket()
        {
            _logger.LogInformation("=== CONNECT 数据包示例 ===");

            // 创建 CONNECT 数据包
            var connectPacket = new MqttConnectPacket
            {
                ProtocolName = MqttProtocolConstants.ProtocolNames.Mqtt311,
                ProtocolVersion = MqttProtocolVersion.Version311,
                ClientId = "ExampleClient123",
                CleanSession = true,
                KeepAlive = 60,
                Username = "testuser",
                Password = "testpass",
                WillMessage = new MqttWillMessage
                {
                    Topic = "client/ExampleClient123/status",
                    Payload = Encoding.UTF8.GetBytes("offline"),
                    QoSLevel = MqttQoSLevel.AtLeastOnce,
                    Retain = true
                }
            };

            // 验证数据包
            var validationResult = _validator.ValidatePacket(connectPacket, MqttProtocolVersion.Version311);
            _logger.LogInformation("CONNECT 数据包验证结果: {IsValid}", validationResult.IsValid);
            if (!validationResult.IsValid)
            {
                _logger.LogError("验证失败: {ErrorMessage}", validationResult.ErrorMessage);
                return;
            }

            // 序列化数据包
            var serializedData = _serializer.SerializePacket(connectPacket, MqttProtocolVersion.Version311);
            _logger.LogInformation("CONNECT 数据包序列化完成，大小: {Size} 字节", serializedData.Length);

            // 解析数据包
            var sequence = new ReadOnlySequence<byte>(serializedData);
            var parsedPacket = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);
            
            if (parsedPacket is MqttConnectPacket parsedConnect)
            {
                _logger.LogInformation("CONNECT 数据包解析成功:");
                _logger.LogInformation("  客户端ID: {ClientId}", parsedConnect.ClientId);
                _logger.LogInformation("  协议版本: {ProtocolVersion}", parsedConnect.ProtocolVersion);
                _logger.LogInformation("  保活时间: {KeepAlive} 秒", parsedConnect.KeepAlive);
                _logger.LogInformation("  清理会话: {CleanSession}", parsedConnect.CleanSession);
                _logger.LogInformation("  用户名: {Username}", parsedConnect.Username);
                
                if (parsedConnect.WillMessage != null)
                {
                    _logger.LogInformation("  遗嘱主题: {WillTopic}", parsedConnect.WillMessage.Topic);
                    _logger.LogInformation("  遗嘱消息: {WillMessage}", 
                        Encoding.UTF8.GetString(parsedConnect.WillMessage.Payload));
                }
            }
            else
            {
                _logger.LogError("CONNECT 数据包解析失败");
            }
        }

        /// <summary>
        /// 演示 PUBLISH 数据包的创建、序列化和解析
        /// </summary>
        public void DemonstratePublishPacket()
        {
            _logger.LogInformation("=== PUBLISH 数据包示例 ===");

            // 创建 PUBLISH 数据包
            var publishPacket = MqttPublishPacket.Create(
                topic: "sensors/temperature",
                payload: Encoding.UTF8.GetBytes("{\"temperature\": 23.5, \"humidity\": 65.2}"),
                qosLevel: MqttQoSLevel.AtLeastOnce,
                retain: false,
                packetIdentifier: 1234
            );

            // 验证数据包
            var validationResult = _validator.ValidatePacket(publishPacket, MqttProtocolVersion.Version311);
            _logger.LogInformation("PUBLISH 数据包验证结果: {IsValid}", validationResult.IsValid);

            // 序列化数据包
            var serializedData = _serializer.SerializePacket(publishPacket, MqttProtocolVersion.Version311);
            _logger.LogInformation("PUBLISH 数据包序列化完成，大小: {Size} 字节", serializedData.Length);

            // 解析数据包
            var sequence = new ReadOnlySequence<byte>(serializedData);
            var parsedPacket = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);
            
            if (parsedPacket is MqttPublishPacket parsedPublish)
            {
                _logger.LogInformation("PUBLISH 数据包解析成功:");
                _logger.LogInformation("  主题: {Topic}", parsedPublish.Topic);
                _logger.LogInformation("  QoS: {QoS}", parsedPublish.QoSLevel);
                _logger.LogInformation("  保留: {Retain}", parsedPublish.Retain);
                _logger.LogInformation("  数据包ID: {PacketId}", parsedPublish.PacketIdentifier);
                _logger.LogInformation("  载荷: {Payload}", parsedPublish.GetPayloadAsString());
            }
            else
            {
                _logger.LogError("PUBLISH 数据包解析失败");
            }
        }

        /// <summary>
        /// 演示 SUBSCRIBE 数据包的创建、序列化和解析
        /// </summary>
        public void DemonstrateSubscribePacket()
        {
            _logger.LogInformation("=== SUBSCRIBE 数据包示例 ===");

            // 创建 SUBSCRIBE 数据包
            var subscribePacket = MqttSubscribePacket.Create(
                packetIdentifier: 5678,
                new MqttSubscription
                {
                    TopicFilter = "sensors/+/temperature",
                    QoSLevel = MqttQoSLevel.AtLeastOnce
                },
                new MqttSubscription
                {
                    TopicFilter = "alerts/#",
                    QoSLevel = MqttQoSLevel.ExactlyOnce
                }
            );

            // 验证数据包
            var validationResult = _validator.ValidatePacket(subscribePacket, MqttProtocolVersion.Version311);
            _logger.LogInformation("SUBSCRIBE 数据包验证结果: {IsValid}", validationResult.IsValid);

            // 序列化数据包
            var serializedData = _serializer.SerializePacket(subscribePacket, MqttProtocolVersion.Version311);
            _logger.LogInformation("SUBSCRIBE 数据包序列化完成，大小: {Size} 字节", serializedData.Length);

            // 解析数据包
            var sequence = new ReadOnlySequence<byte>(serializedData);
            var parsedPacket = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);
            
            if (parsedPacket is MqttSubscribePacket parsedSubscribe)
            {
                _logger.LogInformation("SUBSCRIBE 数据包解析成功:");
                _logger.LogInformation("  数据包ID: {PacketId}", parsedSubscribe.PacketIdentifier);
                _logger.LogInformation("  订阅数量: {Count}", parsedSubscribe.Subscriptions.Count);
                
                for (int i = 0; i < parsedSubscribe.Subscriptions.Count; i++)
                {
                    var subscription = parsedSubscribe.Subscriptions[i];
                    _logger.LogInformation("  订阅 {Index}: {TopicFilter} (QoS {QoS})", 
                        i + 1, subscription.TopicFilter, subscription.QoSLevel);
                }
            }
            else
            {
                _logger.LogError("SUBSCRIBE 数据包解析失败");
            }
        }

        /// <summary>
        /// 演示主题匹配功能
        /// </summary>
        public void DemonstrateTopicMatching()
        {
            _logger.LogInformation("=== 主题匹配示例 ===");

            var topicFilters = new[]
            {
                "sensors/+/temperature",
                "sensors/+/humidity",
                "alerts/#",
                "system/status",
                "$SYS/broker/uptime"
            };

            var topicNames = new[]
            {
                "sensors/room1/temperature",
                "sensors/room2/humidity",
                "alerts/fire/building1",
                "alerts/security/door",
                "system/status",
                "system/config",
                "$SYS/broker/uptime",
                "$SYS/broker/clients"
            };

            _logger.LogInformation("主题过滤器:");
            foreach (var filter in topicFilters)
            {
                _logger.LogInformation("  {Filter}", filter);
            }

            _logger.LogInformation("主题匹配结果:");
            foreach (var topicName in topicNames)
            {
                var matchingFilters = _topicMatcher.GetMatchingFilters(topicFilters, topicName);
                _logger.LogInformation("  {TopicName} -> [{Matches}]", 
                    topicName, string.Join(", ", matchingFilters));
            }
        }

        /// <summary>
        /// 演示心跳数据包
        /// </summary>
        public void DemonstratePingPackets()
        {
            _logger.LogInformation("=== 心跳数据包示例 ===");

            // PINGREQ
            var pingReq = MqttPingReqPacket.Create();
            var pingReqData = _serializer.SerializePacket(pingReq, MqttProtocolVersion.Version311);
            _logger.LogInformation("PINGREQ 数据包大小: {Size} 字节", pingReqData.Length);

            // PINGRESP
            var pingResp = MqttPingRespPacket.Create();
            var pingRespData = _serializer.SerializePacket(pingResp, MqttProtocolVersion.Version311);
            _logger.LogInformation("PINGRESP 数据包大小: {Size} 字节", pingRespData.Length);

            // 解析验证
            var pingReqSequence = new ReadOnlySequence<byte>(pingReqData);
            var parsedPingReq = _parser.ParsePacket(pingReqSequence, MqttProtocolVersion.Version311);
            _logger.LogInformation("PINGREQ 解析结果: {Type}", parsedPingReq?.PacketType);

            var pingRespSequence = new ReadOnlySequence<byte>(pingRespData);
            var parsedPingResp = _parser.ParsePacket(pingRespSequence, MqttProtocolVersion.Version311);
            _logger.LogInformation("PINGRESP 解析结果: {Type}", parsedPingResp?.PacketType);
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public void RunAllExamples()
        {
            _logger.LogInformation("开始运行 MQTT 协议解析器示例...");
            
            try
            {
                DemonstrateConnectPacket();
                _logger.LogInformation("");
                
                DemonstratePublishPacket();
                _logger.LogInformation("");
                
                DemonstrateSubscribePacket();
                _logger.LogInformation("");
                
                DemonstrateTopicMatching();
                _logger.LogInformation("");
                
                DemonstratePingPackets();
                
                _logger.LogInformation("所有示例运行完成！");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "运行示例时发生错误");
            }
        }
    }
}
