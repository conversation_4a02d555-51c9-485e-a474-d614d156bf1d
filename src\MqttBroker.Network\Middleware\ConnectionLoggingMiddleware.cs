using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol;
using MqttBroker.Network.Abstractions;

namespace MqttBroker.Network.Middleware;

/// <summary>
/// 连接日志记录中间件
/// </summary>
public class ConnectionLoggingMiddleware : INetworkMiddleware
{
    private readonly ILogger<ConnectionLoggingMiddleware> _logger;

    /// <summary>
    /// 初始化连接日志记录中间件
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ConnectionLoggingMiddleware(ILogger<ConnectionLoggingMiddleware> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 中间件名称
    /// </summary>
    public string Name => "ConnectionLogging";

    /// <summary>
    /// 中间件优先级（数值越小优先级越高）
    /// </summary>
    public int Priority => 1000; // 较低优先级，用于日志记录

    /// <summary>
    /// 处理连接建立
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="next">下一个中间件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task OnConnectionEstablishedAsync(IClientConnection connection, Func<Task> next, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Connection established: {ConnectionId} from {RemoteEndPoint}", 
            connection.Id, connection.RemoteEndPoint);

        await next();
    }

    /// <summary>
    /// 处理数据包接收
    /// </summary>
    /// <param name="packet">接收到的数据包</param>
    /// <param name="connection">客户端连接</param>
    /// <param name="next">下一个中间件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task OnPacketReceivedAsync(IMqttPacket packet, IClientConnection connection, Func<Task> next, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Packet received: {PacketType} from {ConnectionId} ({ClientId})", 
            packet.PacketType, connection.Id, connection.ClientId ?? "Unknown");

        await next();
    }

    /// <summary>
    /// 处理数据包发送
    /// </summary>
    /// <param name="packet">要发送的数据包</param>
    /// <param name="connection">客户端连接</param>
    /// <param name="next">下一个中间件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task OnPacketSendingAsync(IMqttPacket packet, IClientConnection connection, Func<Task> next, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Packet sending: {PacketType} to {ConnectionId} ({ClientId})", 
            packet.PacketType, connection.Id, connection.ClientId ?? "Unknown");

        await next();
    }

    /// <summary>
    /// 处理连接关闭
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="reason">关闭原因</param>
    /// <param name="next">下一个中间件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task OnConnectionClosedAsync(IClientConnection connection, DisconnectionReason reason, Func<Task> next, CancellationToken cancellationToken = default)
    {
        var duration = DateTime.UtcNow - connection.ConnectedAt;
        
        _logger.LogInformation("Connection closed: {ConnectionId} ({ClientId}) from {RemoteEndPoint}, Reason={Reason}, Duration={Duration}", 
            connection.Id, connection.ClientId ?? "Unknown", connection.RemoteEndPoint, reason, duration);

        await next();
    }
}
