using System;
using System.Buffers;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Protocol
{
    /// <summary>
    /// MQTT 数据包序列化器实现
    /// </summary>
    public class MqttPacketSerializer : IMqttPacketSerializer
    {
        private readonly ILogger<MqttPacketSerializer> _logger;

        /// <summary>
        /// 初始化 MQTT 数据包序列化器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public MqttPacketSerializer(ILogger<MqttPacketSerializer> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 序列化 MQTT 数据包
        /// </summary>
        /// <param name="packet">要序列化的数据包</param>
        /// <param name="protocolVersion">MQTT 协议版本</param>
        /// <returns>序列化后的字节数组</returns>
        public byte[] SerializePacket(IMqttPacket packet, MqttProtocolVersion protocolVersion)
        {
            if (packet == null)
                throw new ArgumentNullException(nameof(packet));

            var bufferSize = GetRequiredBufferSize(packet, protocolVersion);
            var buffer = new byte[bufferSize];
            var bytesWritten = SerializePacket(packet, buffer, protocolVersion);
            
            if (bytesWritten != bufferSize)
            {
                Array.Resize(ref buffer, bytesWritten);
            }

            return buffer;
        }

        /// <summary>
        /// 序列化 MQTT 数据包到指定的缓冲区
        /// </summary>
        /// <param name="packet">要序列化的数据包</param>
        /// <param name="buffer">目标缓冲区</param>
        /// <param name="protocolVersion">MQTT 协议版本</param>
        /// <returns>写入的字节数</returns>
        public int SerializePacket(IMqttPacket packet, Span<byte> buffer, MqttProtocolVersion protocolVersion)
        {
            if (packet == null)
                throw new ArgumentNullException(nameof(packet));

            try
            {
                var writer = new MqttBinaryWriter(buffer);

                _logger.LogDebug("Serializing packet: Type={PacketType}, ProtocolVersion={ProtocolVersion}", 
                    packet.PacketType, protocolVersion);

                // 序列化固定头部
                SerializeFixedHeader(writer, packet);

                // 序列化可变头部和有效载荷
                switch (packet)
                {
                    case MqttConnectPacket connectPacket:
                        SerializeConnectPacket(writer, connectPacket, protocolVersion);
                        break;
                    case MqttConnAckPacket connAckPacket:
                        SerializeConnAckPacket(writer, connAckPacket, protocolVersion);
                        break;
                    case MqttPublishPacket publishPacket:
                        SerializePublishPacket(writer, publishPacket, protocolVersion);
                        break;
                    case MqttPubAckPacket pubAckPacket:
                        SerializePubAckPacket(writer, pubAckPacket, protocolVersion);
                        break;
                    case MqttPubRecPacket pubRecPacket:
                        SerializePubRecPacket(writer, pubRecPacket, protocolVersion);
                        break;
                    case MqttPubRelPacket pubRelPacket:
                        SerializePubRelPacket(writer, pubRelPacket, protocolVersion);
                        break;
                    case MqttPubCompPacket pubCompPacket:
                        SerializePubCompPacket(writer, pubCompPacket, protocolVersion);
                        break;
                    case MqttSubscribePacket subscribePacket:
                        SerializeSubscribePacket(writer, subscribePacket, protocolVersion);
                        break;
                    case MqttSubAckPacket subAckPacket:
                        SerializeSubAckPacket(writer, subAckPacket, protocolVersion);
                        break;
                    case MqttUnsubscribePacket unsubscribePacket:
                        SerializeUnsubscribePacket(writer, unsubscribePacket, protocolVersion);
                        break;
                    case MqttUnsubAckPacket unsubAckPacket:
                        SerializeUnsubAckPacket(writer, unsubAckPacket, protocolVersion);
                        break;
                    case MqttPingReqPacket:
                        // PINGREQ 只有固定头部
                        break;
                    case MqttPingRespPacket:
                        // PINGRESP 只有固定头部
                        break;
                    case MqttDisconnectPacket disconnectPacket:
                        SerializeDisconnectPacket(writer, disconnectPacket, protocolVersion);
                        break;
                    case MqttAuthPacket authPacket:
                        SerializeAuthPacket(writer, authPacket, protocolVersion);
                        break;
                    default:
                        throw new NotSupportedException($"Packet type {packet.GetType().Name} is not supported");
                }

                return writer.Written;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error serializing MQTT packet of type {PacketType}", packet.PacketType);
                throw;
            }
        }

        /// <summary>
        /// 获取序列化数据包所需的缓冲区大小
        /// </summary>
        /// <param name="packet">要序列化的数据包</param>
        /// <param name="protocolVersion">MQTT 协议版本</param>
        /// <returns>所需的缓冲区大小</returns>
        public int GetRequiredBufferSize(IMqttPacket packet, MqttProtocolVersion protocolVersion)
        {
            if (packet == null)
                throw new ArgumentNullException(nameof(packet));

            return packet.GetEstimatedSize();
        }

        #region 私有序列化方法

        private void SerializeFixedHeader(MqttBinaryWriter writer, IMqttPacket packet)
        {
            // 写入数据包类型和标志位
            var firstByte = (byte)(((byte)packet.PacketType << 4) | (packet.Flags & 0x0F));
            writer.WriteByte(firstByte);

            // 计算剩余长度
            var remainingLength = CalculateRemainingLength(packet);
            writer.WriteVariableByteInteger(remainingLength);
        }

        private int CalculateRemainingLength(IMqttPacket packet)
        {
            // 估计大小减去固定头部的大小
            var estimatedSize = packet.GetEstimatedSize();
            var headerSize = 1 + GetVariableByteIntegerLength(estimatedSize - 1);
            return estimatedSize - headerSize;
        }

        private static int GetVariableByteIntegerLength(int value)
        {
            if (value < 0 || value > MqttProtocolConstants.PacketSizeLimits.MaxPacketSize)
                throw new ArgumentOutOfRangeException(nameof(value));

            if (value < 128) return 1;
            if (value < 16384) return 2;
            if (value < 2097152) return 3;
            return 4;
        }

        private void SerializeConnectPacket(MqttBinaryWriter writer, MqttConnectPacket packet, MqttProtocolVersion protocolVersion)
        {
            // 可变头部
            writer.WriteString(packet.ProtocolName);
            writer.WriteByte((byte)packet.ProtocolVersion);
            writer.WriteByte(packet.ConnectFlags);
            writer.WriteUInt16(packet.KeepAlive);

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                SerializeProperties(writer, packet.Properties);
            }

            // 有效载荷
            writer.WriteString(packet.ClientId);

            if (packet.WillMessage != null)
            {
                // MQTT 5.0 遗嘱属性
                if (protocolVersion == MqttProtocolVersion.Version50)
                {
                    SerializeProperties(writer, packet.WillMessage.Properties);
                }

                writer.WriteString(packet.WillMessage.Topic);
                writer.WriteBinaryData(packet.WillMessage.Payload);
            }

            if (!string.IsNullOrEmpty(packet.Username))
            {
                writer.WriteString(packet.Username);
            }

            if (!string.IsNullOrEmpty(packet.Password))
            {
                writer.WriteString(packet.Password);
            }
        }

        private void SerializeConnAckPacket(MqttBinaryWriter writer, MqttConnAckPacket packet, MqttProtocolVersion protocolVersion)
        {
            writer.WriteByte(packet.AcknowledgeFlags);

            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                writer.WriteByte((byte)packet.ReasonCode);
                SerializeProperties(writer, packet.Properties);
            }
            else
            {
                writer.WriteByte((byte)packet.ReturnCode);
            }
        }

        private void SerializePublishPacket(MqttBinaryWriter writer, MqttPublishPacket packet, MqttProtocolVersion protocolVersion)
        {
            // 可变头部
            writer.WriteString(packet.Topic);

            if (packet.QoSLevel > MqttQoSLevel.AtMostOnce)
            {
                writer.WriteUInt16(packet.PacketIdentifier!.Value);
            }

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                SerializeProperties(writer, packet.Properties);
            }

            // 有效载荷
            if (packet.Payload != null && packet.Payload.Length > 0)
            {
                writer.WriteBytes(packet.Payload);
            }
        }

        private void SerializePubAckPacket(MqttBinaryWriter writer, MqttPubAckPacket packet, MqttProtocolVersion protocolVersion)
        {
            writer.WriteUInt16(packet.PacketIdentifier!.Value);

            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                if (packet.ReasonCode != MqttReasonCode.Success || packet.Properties != null)
                {
                    writer.WriteByte((byte)packet.ReasonCode);
                    SerializeProperties(writer, packet.Properties);
                }
            }
        }

        private void SerializePubRecPacket(MqttBinaryWriter writer, MqttPubRecPacket packet, MqttProtocolVersion protocolVersion)
        {
            writer.WriteUInt16(packet.PacketIdentifier!.Value);

            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                if (packet.ReasonCode != MqttReasonCode.Success || packet.Properties != null)
                {
                    writer.WriteByte((byte)packet.ReasonCode);
                    SerializeProperties(writer, packet.Properties);
                }
            }
        }

        private void SerializePubRelPacket(MqttBinaryWriter writer, MqttPubRelPacket packet, MqttProtocolVersion protocolVersion)
        {
            writer.WriteUInt16(packet.PacketIdentifier!.Value);

            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                if (packet.ReasonCode != MqttReasonCode.Success || packet.Properties != null)
                {
                    writer.WriteByte((byte)packet.ReasonCode);
                    SerializeProperties(writer, packet.Properties);
                }
            }
        }

        private void SerializePubCompPacket(MqttBinaryWriter writer, MqttPubCompPacket packet, MqttProtocolVersion protocolVersion)
        {
            writer.WriteUInt16(packet.PacketIdentifier!.Value);

            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                if (packet.ReasonCode != MqttReasonCode.Success || packet.Properties != null)
                {
                    writer.WriteByte((byte)packet.ReasonCode);
                    SerializeProperties(writer, packet.Properties);
                }
            }
        }

        private void SerializeProperties(MqttBinaryWriter writer, MqttProperties? properties)
        {
            if (properties == null || properties.Properties.Count == 0)
            {
                writer.WriteVariableByteInteger(0);
                return;
            }

            // 计算属性长度
            var propertiesLength = CalculatePropertiesLength(properties);
            writer.WriteVariableByteInteger(propertiesLength);

            // 写入属性
            foreach (var property in properties.Properties)
            {
                writer.WriteByte((byte)property.Key);
                WritePropertyValue(writer, property.Key, property.Value);
            }
        }

        private void SerializeSubscribePacket(MqttBinaryWriter writer, MqttSubscribePacket packet, MqttProtocolVersion protocolVersion)
        {
            writer.WriteUInt16(packet.PacketIdentifier!.Value);

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                SerializeProperties(writer, packet.Properties);
            }

            // 有效载荷 - 订阅列表
            foreach (var subscription in packet.Subscriptions)
            {
                writer.WriteString(subscription.TopicFilter);
                writer.WriteByte(subscription.GetOptionsAsByte());
            }
        }

        private void SerializeSubAckPacket(MqttBinaryWriter writer, MqttSubAckPacket packet, MqttProtocolVersion protocolVersion)
        {
            writer.WriteUInt16(packet.PacketIdentifier!.Value);

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                SerializeProperties(writer, packet.Properties);
            }

            // 有效载荷 - 返回码列表
            foreach (var reasonCode in packet.ReasonCodes)
            {
                writer.WriteByte((byte)reasonCode);
            }
        }

        private void SerializeUnsubscribePacket(MqttBinaryWriter writer, MqttUnsubscribePacket packet, MqttProtocolVersion protocolVersion)
        {
            writer.WriteUInt16(packet.PacketIdentifier!.Value);

            // MQTT 5.0 属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                SerializeProperties(writer, packet.Properties);
            }

            // 有效载荷 - 主题过滤器列表
            foreach (var topicFilter in packet.TopicFilters)
            {
                writer.WriteString(topicFilter);
            }
        }

        private void SerializeUnsubAckPacket(MqttBinaryWriter writer, MqttUnsubAckPacket packet, MqttProtocolVersion protocolVersion)
        {
            writer.WriteUInt16(packet.PacketIdentifier!.Value);

            // MQTT 5.0 属性和原因码
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                SerializeProperties(writer, packet.Properties);

                // 有效载荷 - 原因码列表
                foreach (var reasonCode in packet.ReasonCodes)
                {
                    writer.WriteByte((byte)reasonCode);
                }
            }
        }

        private void SerializeDisconnectPacket(MqttBinaryWriter writer, MqttDisconnectPacket packet, MqttProtocolVersion protocolVersion)
        {
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                if (packet.ReasonCode != MqttReasonCode.NormalDisconnection || packet.Properties != null)
                {
                    writer.WriteByte((byte)packet.ReasonCode);
                    SerializeProperties(writer, packet.Properties);
                }
            }
        }

        private void SerializeAuthPacket(MqttBinaryWriter writer, MqttAuthPacket packet, MqttProtocolVersion protocolVersion)
        {
            if (protocolVersion != MqttProtocolVersion.Version50)
                throw new NotSupportedException("AUTH packet is only supported in MQTT 5.0");

            if (packet.ReasonCode != MqttReasonCode.Success || packet.Properties != null)
            {
                writer.WriteByte((byte)packet.ReasonCode);
                SerializeProperties(writer, packet.Properties);
            }
        }

        private int CalculatePropertiesLength(MqttProperties properties)
        {
            int length = 0;

            foreach (var property in properties.Properties)
            {
                length += 1; // 属性标识符
                length += GetPropertyValueLength(property.Key, property.Value);
            }

            return length;
        }

        private int GetPropertyValueLength(MqttPropertyType propertyType, object value)
        {
            return propertyType switch
            {
                MqttPropertyType.PayloadFormatIndicator => 1,
                MqttPropertyType.MessageExpiryInterval => 4,
                MqttPropertyType.ContentType => GetStringLength(value as string),
                MqttPropertyType.ResponseTopic => GetStringLength(value as string),
                MqttPropertyType.CorrelationData => GetBinaryDataLength(value as byte[]),
                MqttPropertyType.SubscriptionIdentifier => GetVariableByteIntegerLength((int)value),
                MqttPropertyType.SessionExpiryInterval => 4,
                MqttPropertyType.AssignedClientIdentifier => GetStringLength(value as string),
                MqttPropertyType.ServerKeepAlive => 2,
                MqttPropertyType.AuthenticationMethod => GetStringLength(value as string),
                MqttPropertyType.AuthenticationData => GetBinaryDataLength(value as byte[]),
                MqttPropertyType.RequestProblemInformation => 1,
                MqttPropertyType.WillDelayInterval => 4,
                MqttPropertyType.RequestResponseInformation => 1,
                MqttPropertyType.ResponseInformation => GetStringLength(value as string),
                MqttPropertyType.ServerReference => GetStringLength(value as string),
                MqttPropertyType.ReasonString => GetStringLength(value as string),
                MqttPropertyType.ReceiveMaximum => 2,
                MqttPropertyType.TopicAliasMaximum => 2,
                MqttPropertyType.TopicAlias => 2,
                MqttPropertyType.MaximumQoS => 1,
                MqttPropertyType.RetainAvailable => 1,
                MqttPropertyType.UserProperty => GetUserPropertyLength((ValueTuple<string, string>)value),
                MqttPropertyType.MaximumPacketSize => 4,
                MqttPropertyType.WildcardSubscriptionAvailable => 1,
                MqttPropertyType.SubscriptionIdentifierAvailable => 1,
                MqttPropertyType.SharedSubscriptionAvailable => 1,
                _ => 0
            };
        }

        private void WritePropertyValue(MqttBinaryWriter writer, MqttPropertyType propertyType, object value)
        {
            switch (propertyType)
            {
                case MqttPropertyType.PayloadFormatIndicator:
                case MqttPropertyType.RequestProblemInformation:
                case MqttPropertyType.RequestResponseInformation:
                case MqttPropertyType.MaximumQoS:
                case MqttPropertyType.RetainAvailable:
                case MqttPropertyType.WildcardSubscriptionAvailable:
                case MqttPropertyType.SubscriptionIdentifierAvailable:
                case MqttPropertyType.SharedSubscriptionAvailable:
                    writer.WriteByte((byte)value);
                    break;

                case MqttPropertyType.ServerKeepAlive:
                case MqttPropertyType.ReceiveMaximum:
                case MqttPropertyType.TopicAliasMaximum:
                case MqttPropertyType.TopicAlias:
                    writer.WriteUInt16((ushort)value);
                    break;

                case MqttPropertyType.MessageExpiryInterval:
                case MqttPropertyType.SessionExpiryInterval:
                case MqttPropertyType.WillDelayInterval:
                case MqttPropertyType.MaximumPacketSize:
                    writer.WriteUInt32((uint)value);
                    break;

                case MqttPropertyType.SubscriptionIdentifier:
                    writer.WriteVariableByteInteger((int)value);
                    break;

                case MqttPropertyType.ContentType:
                case MqttPropertyType.ResponseTopic:
                case MqttPropertyType.AssignedClientIdentifier:
                case MqttPropertyType.AuthenticationMethod:
                case MqttPropertyType.ResponseInformation:
                case MqttPropertyType.ServerReference:
                case MqttPropertyType.ReasonString:
                    writer.WriteString(value as string);
                    break;

                case MqttPropertyType.CorrelationData:
                case MqttPropertyType.AuthenticationData:
                    writer.WriteBinaryData(value as byte[]);
                    break;

                case MqttPropertyType.UserProperty:
                    var userProperty = ((string, string))value;
                    writer.WriteString(userProperty.Item1);
                    writer.WriteString(userProperty.Item2);
                    break;

                default:
                    throw new NotSupportedException($"Property type {propertyType} is not supported");
            }
        }

        private static int GetStringLength(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return 2; // 只有长度前缀

            var utf8ByteCount = System.Text.Encoding.UTF8.GetByteCount(value);
            return 2 + utf8ByteCount; // 2字节长度前缀 + UTF-8字节
        }

        private static int GetBinaryDataLength(byte[]? data)
        {
            if (data == null || data.Length == 0)
                return 2; // 只有长度前缀

            return 2 + data.Length; // 2字节长度前缀 + 数据长度
        }

        private static int GetUserPropertyLength(ValueTuple<string, string> userProperty)
        {
            return GetStringLength(userProperty.Item1) + GetStringLength(userProperty.Item2);
        }

        #endregion
    }
}
