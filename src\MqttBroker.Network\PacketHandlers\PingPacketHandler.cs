using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Network.Abstractions;

namespace MqttBroker.Network.PacketHandlers;

/// <summary>
/// PING 数据包处理器
/// </summary>
public class PingPacketHandler : IPacketHandler
{
    private readonly ILogger<PingPacketHandler> _logger;

    /// <summary>
    /// 初始化 PING 数据包处理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public PingPacketHandler(ILogger<PingPacketHandler> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 处理器支持的数据包类型
    /// </summary>
    public MqttPacketType PacketType => MqttPacketType.PingReq;

    /// <summary>
    /// 处理器优先级（数值越小优先级越高）
    /// </summary>
    public int Priority => 100;

    /// <summary>
    /// 检查是否可以处理指定的数据包
    /// </summary>
    /// <param name="packet">要检查的数据包</param>
    /// <param name="connection">客户端连接</param>
    /// <returns>如果可以处理则返回 true，否则返回 false</returns>
    public bool CanHandle(IMqttPacket packet, IClientConnection connection)
    {
        return packet is MqttPingReqPacket && connection.State == ConnectionState.Connected;
    }

    /// <summary>
    /// 处理数据包
    /// </summary>
    /// <param name="packet">要处理的数据包</param>
    /// <param name="connection">客户端连接</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task HandleAsync(IMqttPacket packet, IClientConnection connection, CancellationToken cancellationToken = default)
    {
        if (packet is not MqttPingReqPacket pingRequest)
        {
            _logger.LogWarning("Invalid packet type for PingPacketHandler: {PacketType}", packet.PacketType);
            return;
        }

        try
        {
            // 更新连接的最后活动时间
            connection.UpdateLastActivity();

            // 创建 PING 响应数据包
            var pingResponse = MqttPingRespPacket.Create();

            // 发送 PING 响应
            await connection.SendPacketAsync(pingResponse, cancellationToken);

            _logger.LogTrace("PING request handled for connection {ConnectionId} ({ClientId})",
                connection.Id, connection.ClientId ?? "Unknown");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling PING request for connection {ConnectionId} ({ClientId})",
                connection.Id, connection.ClientId ?? "Unknown");
            throw;
        }
    }
}
