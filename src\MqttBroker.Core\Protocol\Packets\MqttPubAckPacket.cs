using System;

namespace MqttBroker.Core.Protocol.Packets
{
    /// <summary>
    /// MQTT PUBACK 数据包 (QoS 1 确认)
    /// </summary>
    public class MqttPubAckPacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.PubAck;

        /// <summary>
        /// 原因码 (MQTT 5.0)
        /// </summary>
        public MqttReasonCode ReasonCode { get; set; } = MqttReasonCode.Success;

        /// <summary>
        /// MQTT 5.0 属性（仅适用于 MQTT 5.0）
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            int size = 1; // 固定头部第一个字节
            size += 2; // 数据包标识符

            // MQTT 5.0 原因码和属性
            if (ReasonCode != MqttReasonCode.Success || Properties != null)
            {
                size += 1; // 原因码

                if (Properties != null)
                {
                    size += Properties.GetEstimatedSize();
                }
            }

            // 剩余长度字段
            size += GetVariableByteIntegerLength(size - 1);

            return size;
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // PUBACK 必须有数据包标识符
            return IsPacketIdentifierValid();
        }

        /// <summary>
        /// 创建 PUBACK 数据包
        /// </summary>
        /// <param name="packetIdentifier">数据包标识符</param>
        /// <param name="reasonCode">原因码</param>
        /// <returns>PUBACK 数据包</returns>
        public static MqttPubAckPacket Create(ushort packetIdentifier, MqttReasonCode reasonCode = MqttReasonCode.Success)
        {
            return new MqttPubAckPacket
            {
                PacketIdentifier = packetIdentifier,
                ReasonCode = reasonCode
            };
        }
    }

    /// <summary>
    /// MQTT PUBREC 数据包 (QoS 2 第一步确认)
    /// </summary>
    public class MqttPubRecPacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.PubRec;

        /// <summary>
        /// 原因码 (MQTT 5.0)
        /// </summary>
        public MqttReasonCode ReasonCode { get; set; } = MqttReasonCode.Success;

        /// <summary>
        /// MQTT 5.0 属性（仅适用于 MQTT 5.0）
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            int size = 1; // 固定头部第一个字节
            size += 2; // 数据包标识符

            // MQTT 5.0 原因码和属性
            if (ReasonCode != MqttReasonCode.Success || Properties != null)
            {
                size += 1; // 原因码

                if (Properties != null)
                {
                    size += Properties.GetEstimatedSize();
                }
            }

            // 剩余长度字段
            size += GetVariableByteIntegerLength(size - 1);

            return size;
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // PUBREC 必须有数据包标识符
            return IsPacketIdentifierValid();
        }

        /// <summary>
        /// 创建 PUBREC 数据包
        /// </summary>
        /// <param name="packetIdentifier">数据包标识符</param>
        /// <param name="reasonCode">原因码</param>
        /// <returns>PUBREC 数据包</returns>
        public static MqttPubRecPacket Create(ushort packetIdentifier, MqttReasonCode reasonCode = MqttReasonCode.Success)
        {
            return new MqttPubRecPacket
            {
                PacketIdentifier = packetIdentifier,
                ReasonCode = reasonCode
            };
        }
    }

    /// <summary>
    /// MQTT PUBREL 数据包 (QoS 2 第二步)
    /// </summary>
    public class MqttPubRelPacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.PubRel;

        /// <summary>
        /// 数据包标志位 (PUBREL 固定为 0x02)
        /// </summary>
        public override byte Flags => 0x02;

        /// <summary>
        /// 原因码 (MQTT 5.0)
        /// </summary>
        public MqttReasonCode ReasonCode { get; set; } = MqttReasonCode.Success;

        /// <summary>
        /// MQTT 5.0 属性（仅适用于 MQTT 5.0）
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            int size = 1; // 固定头部第一个字节
            size += 2; // 数据包标识符

            // MQTT 5.0 原因码和属性
            if (ReasonCode != MqttReasonCode.Success || Properties != null)
            {
                size += 1; // 原因码

                if (Properties != null)
                {
                    size += Properties.GetEstimatedSize();
                }
            }

            // 剩余长度字段
            size += GetVariableByteIntegerLength(size - 1);

            return size;
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // PUBREL 必须有数据包标识符
            return IsPacketIdentifierValid();
        }

        /// <summary>
        /// 创建 PUBREL 数据包
        /// </summary>
        /// <param name="packetIdentifier">数据包标识符</param>
        /// <param name="reasonCode">原因码</param>
        /// <returns>PUBREL 数据包</returns>
        public static MqttPubRelPacket Create(ushort packetIdentifier, MqttReasonCode reasonCode = MqttReasonCode.Success)
        {
            return new MqttPubRelPacket
            {
                PacketIdentifier = packetIdentifier,
                ReasonCode = reasonCode
            };
        }
    }

    /// <summary>
    /// MQTT PUBCOMP 数据包 (QoS 2 第三步确认)
    /// </summary>
    public class MqttPubCompPacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.PubComp;

        /// <summary>
        /// 原因码 (MQTT 5.0)
        /// </summary>
        public MqttReasonCode ReasonCode { get; set; } = MqttReasonCode.Success;

        /// <summary>
        /// MQTT 5.0 属性（仅适用于 MQTT 5.0）
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            int size = 1; // 固定头部第一个字节
            size += 2; // 数据包标识符

            // MQTT 5.0 原因码和属性
            if (ReasonCode != MqttReasonCode.Success || Properties != null)
            {
                size += 1; // 原因码

                if (Properties != null)
                {
                    size += Properties.GetEstimatedSize();
                }
            }

            // 剩余长度字段
            size += GetVariableByteIntegerLength(size - 1);

            return size;
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // PUBCOMP 必须有数据包标识符
            return IsPacketIdentifierValid();
        }

        /// <summary>
        /// 创建 PUBCOMP 数据包
        /// </summary>
        /// <param name="packetIdentifier">数据包标识符</param>
        /// <param name="reasonCode">原因码</param>
        /// <returns>PUBCOMP 数据包</returns>
        public static MqttPubCompPacket Create(ushort packetIdentifier, MqttReasonCode reasonCode = MqttReasonCode.Success)
        {
            return new MqttPubCompPacket
            {
                PacketIdentifier = packetIdentifier,
                ReasonCode = reasonCode
            };
        }
    }
}
