using System.Collections.Concurrent;
using System.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol;
using MqttBroker.Network.Abstractions;

namespace MqttBroker.Network.PacketHandlers;

/// <summary>
/// 数据包处理器管理器实现
/// </summary>
public class PacketHandlerManager : IPacketHandlerManager
{
    private readonly ILogger<PacketHandlerManager> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly ConcurrentDictionary<MqttPacketType, List<IPacketHandler>> _handlers = new();
    private readonly PacketHandlerStatistics _statistics = new();
    private readonly object _lockObject = new();

    /// <summary>
    /// 初始化数据包处理器管理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="serviceProvider">服务提供者</param>
    public PacketHandlerManager(
        ILogger<PacketHandlerManager> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
    }

    /// <summary>
    /// 注册数据包处理器
    /// </summary>
    /// <param name="handler">数据包处理器</param>
    public void RegisterHandler(IPacketHandler handler)
    {
        if (handler == null)
            throw new ArgumentNullException(nameof(handler));

        lock (_lockObject)
        {
            var handlers = _handlers.GetOrAdd(handler.PacketType, _ => new List<IPacketHandler>());
            
            // 按优先级插入
            var insertIndex = handlers.FindIndex(h => h.Priority > handler.Priority);
            if (insertIndex == -1)
            {
                handlers.Add(handler);
            }
            else
            {
                handlers.Insert(insertIndex, handler);
            }

            _statistics.RegisteredHandlers = _handlers.Values.Sum(list => list.Count);

            _logger.LogDebug("Registered packet handler: {HandlerType} for {PacketType} with priority {Priority}", 
                handler.GetType().Name, handler.PacketType, handler.Priority);
        }
    }

    /// <summary>
    /// 注册数据包处理器
    /// </summary>
    /// <typeparam name="T">处理器类型</typeparam>
    public void RegisterHandler<T>() where T : class, IPacketHandler
    {
        var handler = _serviceProvider.GetRequiredService<T>();
        RegisterHandler(handler);
    }

    /// <summary>
    /// 取消注册数据包处理器
    /// </summary>
    /// <param name="packetType">数据包类型</param>
    /// <param name="handlerType">处理器类型</param>
    public void UnregisterHandler(MqttPacketType packetType, Type handlerType)
    {
        if (handlerType == null)
            throw new ArgumentNullException(nameof(handlerType));

        lock (_lockObject)
        {
            if (_handlers.TryGetValue(packetType, out var handlers))
            {
                var removed = handlers.RemoveAll(h => h.GetType() == handlerType);
                
                if (removed > 0)
                {
                    _statistics.RegisteredHandlers = _handlers.Values.Sum(list => list.Count);
                    
                    _logger.LogDebug("Unregistered {Count} packet handlers: {HandlerType} for {PacketType}", 
                        removed, handlerType.Name, packetType);
                }

                // 如果列表为空，移除整个条目
                if (handlers.Count == 0)
                {
                    _handlers.TryRemove(packetType, out _);
                }
            }
        }
    }

    /// <summary>
    /// 获取指定数据包类型的处理器
    /// </summary>
    /// <param name="packetType">数据包类型</param>
    /// <returns>数据包处理器列表</returns>
    public IEnumerable<IPacketHandler> GetHandlers(MqttPacketType packetType)
    {
        if (_handlers.TryGetValue(packetType, out var handlers))
        {
            return handlers.ToList(); // 返回副本以避免并发修改
        }

        return Enumerable.Empty<IPacketHandler>();
    }

    /// <summary>
    /// 处理数据包
    /// </summary>
    /// <param name="packet">要处理的数据包</param>
    /// <param name="connection">客户端连接</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task HandlePacketAsync(IMqttPacket packet, IClientConnection connection, CancellationToken cancellationToken = default)
    {
        if (packet == null)
            throw new ArgumentNullException(nameof(packet));
        
        if (connection == null)
            throw new ArgumentNullException(nameof(connection));

        var stopwatch = Stopwatch.StartNew();
        var handled = false;

        try
        {
            var handlers = GetHandlers(packet.PacketType);
            
            foreach (var handler in handlers)
            {
                try
                {
                    if (handler.CanHandle(packet, connection))
                    {
                        await handler.HandleAsync(packet, connection, cancellationToken);
                        handled = true;

                        _logger.LogTrace("Packet handled by {HandlerType}: {PacketType}, ConnectionId={ConnectionId}", 
                            handler.GetType().Name, packet.PacketType, connection.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in packet handler {HandlerType} for {PacketType}, ConnectionId={ConnectionId}", 
                        handler.GetType().Name, packet.PacketType, connection.Id);
                    
                    UpdatePacketTypeStatistics(packet.PacketType, stopwatch.ElapsedMilliseconds, false);
                    _statistics.FailedPackets++;
                    
                    throw; // 重新抛出异常以便上层处理
                }
            }

            if (!handled)
            {
                _logger.LogWarning("No handler found for packet type: {PacketType}, ConnectionId={ConnectionId}", 
                    packet.PacketType, connection.Id);
            }

            UpdatePacketTypeStatistics(packet.PacketType, stopwatch.ElapsedMilliseconds, true);
            _statistics.TotalPacketsHandled++;
            _statistics.LastHandlingTime = DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error handling packet: {PacketType}, ConnectionId={ConnectionId}", 
                packet.PacketType, connection.Id);
            
            UpdatePacketTypeStatistics(packet.PacketType, stopwatch.ElapsedMilliseconds, false);
            _statistics.FailedPackets++;
            
            throw;
        }
        finally
        {
            stopwatch.Stop();
            
            // 更新平均处理时间
            if (_statistics.TotalPacketsHandled > 0)
            {
                _statistics.AverageHandlingTime = (_statistics.AverageHandlingTime * (_statistics.TotalPacketsHandled - 1) + stopwatch.ElapsedMilliseconds) / _statistics.TotalPacketsHandled;
            }
        }
    }

    /// <summary>
    /// 获取处理器统计信息
    /// </summary>
    /// <returns>处理器统计信息</returns>
    public PacketHandlerStatistics GetStatistics()
    {
        // 计算每秒处理的数据包数
        if (_statistics.LastHandlingTime != default)
        {
            var elapsed = DateTime.UtcNow - _statistics.LastHandlingTime;
            if (elapsed.TotalSeconds > 0)
            {
                _statistics.PacketsPerSecond = _statistics.TotalPacketsHandled / elapsed.TotalSeconds;
            }
        }

        return _statistics;
    }

    /// <summary>
    /// 更新数据包类型统计信息
    /// </summary>
    /// <param name="packetType">数据包类型</param>
    /// <param name="handlingTime">处理时间（毫秒）</param>
    /// <param name="success">是否成功</param>
    private void UpdatePacketTypeStatistics(MqttPacketType packetType, double handlingTime, bool success)
    {
        var stats = _statistics.PacketTypeStatistics.GetOrAdd(packetType, _ => new PacketTypeStatistics
        {
            PacketType = packetType,
            MinHandlingTime = double.MaxValue,
            MaxHandlingTime = 0
        });

        if (success)
        {
            stats.HandledCount++;
        }
        else
        {
            stats.FailedCount++;
        }

        // 更新处理时间统计
        if (handlingTime < stats.MinHandlingTime)
        {
            stats.MinHandlingTime = handlingTime;
        }

        if (handlingTime > stats.MaxHandlingTime)
        {
            stats.MaxHandlingTime = handlingTime;
        }

        // 更新平均处理时间
        var totalCount = stats.HandledCount + stats.FailedCount;
        if (totalCount > 0)
        {
            stats.AverageHandlingTime = (stats.AverageHandlingTime * (totalCount - 1) + handlingTime) / totalCount;
        }

        stats.LastHandlingTime = DateTime.UtcNow;
    }
}
