{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "MqttBroker": {"Server": {"Port": 1883, "SecurePort": 8883, "MaxConnections": 10000, "KeepAliveTimeout": 60, "ConnectionTimeout": 30}, "Storage": {"ConnectionString": "Data Source=data/mqtt_broker.db", "EnablePersistence": true}, "Security": {"AllowAnonymous": true, "RequireAuthentication": false}, "Performance": {"MaxMessageSize": 268435456, "MaxTopicLength": 65535, "MaxClientIdLength": 23}}}