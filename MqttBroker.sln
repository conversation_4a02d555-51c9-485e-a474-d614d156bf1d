﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MqttBroker.Core", "src\MqttBroker.Core\MqttBroker.Core.csproj", "{CFB7488C-7A59-4EC7-B77B-FC910E988282}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MqttBroker.Network", "src\MqttBroker.Network\MqttBroker.Network.csproj", "{DE39061C-CE49-434F-BB00-947C5A3F91FF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MqttBroker.Storage", "src\MqttBroker.Storage\MqttBroker.Storage.csproj", "{24882A6B-C5DF-43CA-8BDA-9BA4CF440736}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MqttBroker.Configuration", "src\MqttBroker.Configuration\MqttBroker.Configuration.csproj", "{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MqttBroker.Logging", "src\MqttBroker.Logging\MqttBroker.Logging.csproj", "{10B0CB12-0EE0-410C-A4CF-C0755D9C9347}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MqttBroker.Metrics", "src\MqttBroker.Metrics\MqttBroker.Metrics.csproj", "{462211D3-9F94-46D0-BC62-5F83DD399CE4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MqttBroker.Host", "src\MqttBroker.Host\MqttBroker.Host.csproj", "{FF4C947D-4E75-48C3-A9A5-76142416D79E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MqttBroker.Tests.Unit", "tests\MqttBroker.Tests.Unit\MqttBroker.Tests.Unit.csproj", "{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MqttBroker.Tests.Integration", "tests\MqttBroker.Tests.Integration\MqttBroker.Tests.Integration.csproj", "{BC4AE91D-7937-4C76-BB00-453FBEB56E05}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MqttBroker.Tests.Performance", "tests\MqttBroker.Tests.Performance\MqttBroker.Tests.Performance.csproj", "{9E091292-718B-4B45-979E-26665C6D58C7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{CFB7488C-7A59-4EC7-B77B-FC910E988282}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CFB7488C-7A59-4EC7-B77B-FC910E988282}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CFB7488C-7A59-4EC7-B77B-FC910E988282}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CFB7488C-7A59-4EC7-B77B-FC910E988282}.Debug|x64.Build.0 = Debug|Any CPU
		{CFB7488C-7A59-4EC7-B77B-FC910E988282}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CFB7488C-7A59-4EC7-B77B-FC910E988282}.Debug|x86.Build.0 = Debug|Any CPU
		{CFB7488C-7A59-4EC7-B77B-FC910E988282}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CFB7488C-7A59-4EC7-B77B-FC910E988282}.Release|Any CPU.Build.0 = Release|Any CPU
		{CFB7488C-7A59-4EC7-B77B-FC910E988282}.Release|x64.ActiveCfg = Release|Any CPU
		{CFB7488C-7A59-4EC7-B77B-FC910E988282}.Release|x64.Build.0 = Release|Any CPU
		{CFB7488C-7A59-4EC7-B77B-FC910E988282}.Release|x86.ActiveCfg = Release|Any CPU
		{CFB7488C-7A59-4EC7-B77B-FC910E988282}.Release|x86.Build.0 = Release|Any CPU
		{DE39061C-CE49-434F-BB00-947C5A3F91FF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DE39061C-CE49-434F-BB00-947C5A3F91FF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DE39061C-CE49-434F-BB00-947C5A3F91FF}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DE39061C-CE49-434F-BB00-947C5A3F91FF}.Debug|x64.Build.0 = Debug|Any CPU
		{DE39061C-CE49-434F-BB00-947C5A3F91FF}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DE39061C-CE49-434F-BB00-947C5A3F91FF}.Debug|x86.Build.0 = Debug|Any CPU
		{DE39061C-CE49-434F-BB00-947C5A3F91FF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DE39061C-CE49-434F-BB00-947C5A3F91FF}.Release|Any CPU.Build.0 = Release|Any CPU
		{DE39061C-CE49-434F-BB00-947C5A3F91FF}.Release|x64.ActiveCfg = Release|Any CPU
		{DE39061C-CE49-434F-BB00-947C5A3F91FF}.Release|x64.Build.0 = Release|Any CPU
		{DE39061C-CE49-434F-BB00-947C5A3F91FF}.Release|x86.ActiveCfg = Release|Any CPU
		{DE39061C-CE49-434F-BB00-947C5A3F91FF}.Release|x86.Build.0 = Release|Any CPU
		{24882A6B-C5DF-43CA-8BDA-9BA4CF440736}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{24882A6B-C5DF-43CA-8BDA-9BA4CF440736}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{24882A6B-C5DF-43CA-8BDA-9BA4CF440736}.Debug|x64.ActiveCfg = Debug|Any CPU
		{24882A6B-C5DF-43CA-8BDA-9BA4CF440736}.Debug|x64.Build.0 = Debug|Any CPU
		{24882A6B-C5DF-43CA-8BDA-9BA4CF440736}.Debug|x86.ActiveCfg = Debug|Any CPU
		{24882A6B-C5DF-43CA-8BDA-9BA4CF440736}.Debug|x86.Build.0 = Debug|Any CPU
		{24882A6B-C5DF-43CA-8BDA-9BA4CF440736}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{24882A6B-C5DF-43CA-8BDA-9BA4CF440736}.Release|Any CPU.Build.0 = Release|Any CPU
		{24882A6B-C5DF-43CA-8BDA-9BA4CF440736}.Release|x64.ActiveCfg = Release|Any CPU
		{24882A6B-C5DF-43CA-8BDA-9BA4CF440736}.Release|x64.Build.0 = Release|Any CPU
		{24882A6B-C5DF-43CA-8BDA-9BA4CF440736}.Release|x86.ActiveCfg = Release|Any CPU
		{24882A6B-C5DF-43CA-8BDA-9BA4CF440736}.Release|x86.Build.0 = Release|Any CPU
		{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61}.Debug|x64.Build.0 = Debug|Any CPU
		{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61}.Debug|x86.Build.0 = Debug|Any CPU
		{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61}.Release|x64.ActiveCfg = Release|Any CPU
		{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61}.Release|x64.Build.0 = Release|Any CPU
		{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61}.Release|x86.ActiveCfg = Release|Any CPU
		{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61}.Release|x86.Build.0 = Release|Any CPU
		{10B0CB12-0EE0-410C-A4CF-C0755D9C9347}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{10B0CB12-0EE0-410C-A4CF-C0755D9C9347}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{10B0CB12-0EE0-410C-A4CF-C0755D9C9347}.Debug|x64.ActiveCfg = Debug|Any CPU
		{10B0CB12-0EE0-410C-A4CF-C0755D9C9347}.Debug|x64.Build.0 = Debug|Any CPU
		{10B0CB12-0EE0-410C-A4CF-C0755D9C9347}.Debug|x86.ActiveCfg = Debug|Any CPU
		{10B0CB12-0EE0-410C-A4CF-C0755D9C9347}.Debug|x86.Build.0 = Debug|Any CPU
		{10B0CB12-0EE0-410C-A4CF-C0755D9C9347}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{10B0CB12-0EE0-410C-A4CF-C0755D9C9347}.Release|Any CPU.Build.0 = Release|Any CPU
		{10B0CB12-0EE0-410C-A4CF-C0755D9C9347}.Release|x64.ActiveCfg = Release|Any CPU
		{10B0CB12-0EE0-410C-A4CF-C0755D9C9347}.Release|x64.Build.0 = Release|Any CPU
		{10B0CB12-0EE0-410C-A4CF-C0755D9C9347}.Release|x86.ActiveCfg = Release|Any CPU
		{10B0CB12-0EE0-410C-A4CF-C0755D9C9347}.Release|x86.Build.0 = Release|Any CPU
		{462211D3-9F94-46D0-BC62-5F83DD399CE4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{462211D3-9F94-46D0-BC62-5F83DD399CE4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{462211D3-9F94-46D0-BC62-5F83DD399CE4}.Debug|x64.ActiveCfg = Debug|Any CPU
		{462211D3-9F94-46D0-BC62-5F83DD399CE4}.Debug|x64.Build.0 = Debug|Any CPU
		{462211D3-9F94-46D0-BC62-5F83DD399CE4}.Debug|x86.ActiveCfg = Debug|Any CPU
		{462211D3-9F94-46D0-BC62-5F83DD399CE4}.Debug|x86.Build.0 = Debug|Any CPU
		{462211D3-9F94-46D0-BC62-5F83DD399CE4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{462211D3-9F94-46D0-BC62-5F83DD399CE4}.Release|Any CPU.Build.0 = Release|Any CPU
		{462211D3-9F94-46D0-BC62-5F83DD399CE4}.Release|x64.ActiveCfg = Release|Any CPU
		{462211D3-9F94-46D0-BC62-5F83DD399CE4}.Release|x64.Build.0 = Release|Any CPU
		{462211D3-9F94-46D0-BC62-5F83DD399CE4}.Release|x86.ActiveCfg = Release|Any CPU
		{462211D3-9F94-46D0-BC62-5F83DD399CE4}.Release|x86.Build.0 = Release|Any CPU
		{FF4C947D-4E75-48C3-A9A5-76142416D79E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FF4C947D-4E75-48C3-A9A5-76142416D79E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FF4C947D-4E75-48C3-A9A5-76142416D79E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FF4C947D-4E75-48C3-A9A5-76142416D79E}.Debug|x64.Build.0 = Debug|Any CPU
		{FF4C947D-4E75-48C3-A9A5-76142416D79E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FF4C947D-4E75-48C3-A9A5-76142416D79E}.Debug|x86.Build.0 = Debug|Any CPU
		{FF4C947D-4E75-48C3-A9A5-76142416D79E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FF4C947D-4E75-48C3-A9A5-76142416D79E}.Release|Any CPU.Build.0 = Release|Any CPU
		{FF4C947D-4E75-48C3-A9A5-76142416D79E}.Release|x64.ActiveCfg = Release|Any CPU
		{FF4C947D-4E75-48C3-A9A5-76142416D79E}.Release|x64.Build.0 = Release|Any CPU
		{FF4C947D-4E75-48C3-A9A5-76142416D79E}.Release|x86.ActiveCfg = Release|Any CPU
		{FF4C947D-4E75-48C3-A9A5-76142416D79E}.Release|x86.Build.0 = Release|Any CPU
		{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B}.Debug|x64.Build.0 = Debug|Any CPU
		{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B}.Debug|x86.Build.0 = Debug|Any CPU
		{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B}.Release|x64.ActiveCfg = Release|Any CPU
		{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B}.Release|x64.Build.0 = Release|Any CPU
		{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B}.Release|x86.ActiveCfg = Release|Any CPU
		{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B}.Release|x86.Build.0 = Release|Any CPU
		{BC4AE91D-7937-4C76-BB00-453FBEB56E05}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BC4AE91D-7937-4C76-BB00-453FBEB56E05}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BC4AE91D-7937-4C76-BB00-453FBEB56E05}.Debug|x64.ActiveCfg = Debug|Any CPU
		{BC4AE91D-7937-4C76-BB00-453FBEB56E05}.Debug|x64.Build.0 = Debug|Any CPU
		{BC4AE91D-7937-4C76-BB00-453FBEB56E05}.Debug|x86.ActiveCfg = Debug|Any CPU
		{BC4AE91D-7937-4C76-BB00-453FBEB56E05}.Debug|x86.Build.0 = Debug|Any CPU
		{BC4AE91D-7937-4C76-BB00-453FBEB56E05}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BC4AE91D-7937-4C76-BB00-453FBEB56E05}.Release|Any CPU.Build.0 = Release|Any CPU
		{BC4AE91D-7937-4C76-BB00-453FBEB56E05}.Release|x64.ActiveCfg = Release|Any CPU
		{BC4AE91D-7937-4C76-BB00-453FBEB56E05}.Release|x64.Build.0 = Release|Any CPU
		{BC4AE91D-7937-4C76-BB00-453FBEB56E05}.Release|x86.ActiveCfg = Release|Any CPU
		{BC4AE91D-7937-4C76-BB00-453FBEB56E05}.Release|x86.Build.0 = Release|Any CPU
		{9E091292-718B-4B45-979E-26665C6D58C7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E091292-718B-4B45-979E-26665C6D58C7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9E091292-718B-4B45-979E-26665C6D58C7}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9E091292-718B-4B45-979E-26665C6D58C7}.Debug|x64.Build.0 = Debug|Any CPU
		{9E091292-718B-4B45-979E-26665C6D58C7}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9E091292-718B-4B45-979E-26665C6D58C7}.Debug|x86.Build.0 = Debug|Any CPU
		{9E091292-718B-4B45-979E-26665C6D58C7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E091292-718B-4B45-979E-26665C6D58C7}.Release|Any CPU.Build.0 = Release|Any CPU
		{9E091292-718B-4B45-979E-26665C6D58C7}.Release|x64.ActiveCfg = Release|Any CPU
		{9E091292-718B-4B45-979E-26665C6D58C7}.Release|x64.Build.0 = Release|Any CPU
		{9E091292-718B-4B45-979E-26665C6D58C7}.Release|x86.ActiveCfg = Release|Any CPU
		{9E091292-718B-4B45-979E-26665C6D58C7}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{CFB7488C-7A59-4EC7-B77B-FC910E988282} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{DE39061C-CE49-434F-BB00-947C5A3F91FF} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{24882A6B-C5DF-43CA-8BDA-9BA4CF440736} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{7A4C1D31-3E35-4E13-BBDC-60F19B23CE61} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{10B0CB12-0EE0-410C-A4CF-C0755D9C9347} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{462211D3-9F94-46D0-BC62-5F83DD399CE4} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{FF4C947D-4E75-48C3-A9A5-76142416D79E} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{F7FAF6E3-7A9C-4520-AD67-0BF3A6B1593B} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{BC4AE91D-7937-4C76-BB00-453FBEB56E05} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{9E091292-718B-4B45-979E-26665C6D58C7} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
EndGlobal
