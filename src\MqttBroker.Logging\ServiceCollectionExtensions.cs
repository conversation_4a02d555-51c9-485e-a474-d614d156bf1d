using Microsoft.Extensions.DependencyInjection;

namespace MqttBroker.Logging;

/// <summary>
/// MQTT Broker 日志服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 MQTT Broker 日志服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttBrokerLogging(this IServiceCollection services)
    {
        // TODO: 注册日志服务
        // 这里将在后续开发中添加具体的服务注册
        
        return services;
    }
}
