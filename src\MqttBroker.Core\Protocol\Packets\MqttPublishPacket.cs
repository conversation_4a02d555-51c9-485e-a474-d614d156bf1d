using System;

namespace MqttBroker.Core.Protocol.Packets
{
    /// <summary>
    /// MQTT PUBLISH 数据包
    /// </summary>
    public class MqttPublishPacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.Publish;

        /// <summary>
        /// 主题名称
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 消息载荷
        /// </summary>
        public byte[] Payload { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// QoS 级别
        /// </summary>
        public MqttQoSLevel QoSLevel { get; set; }

        /// <summary>
        /// 保留标志
        /// </summary>
        public bool Retain { get; set; }

        /// <summary>
        /// 重复标志
        /// </summary>
        public bool Duplicate { get; set; }

        /// <summary>
        /// MQTT 5.0 属性（仅适用于 MQTT 5.0）
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 数据包标志位
        /// </summary>
        public override byte Flags
        {
            get
            {
                byte flags = 0;

                if (Duplicate)
                    flags |= 0x08;

                flags |= (byte)((byte)QoSLevel << 1);

                if (Retain)
                    flags |= 0x01;

                return flags;
            }
            protected set => base.Flags = value;
        }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            int size = 1; // 固定头部第一个字节

            // 可变头部
            size += GetStringLength(Topic); // 主题名称

            // 数据包标识符（仅当 QoS > 0 时）
            if (QoSLevel > MqttQoSLevel.AtMostOnce)
                size += 2;

            // MQTT 5.0 属性
            if (Properties != null)
            {
                size += Properties.GetEstimatedSize();
            }

            // 有效载荷
            if (Payload != null)
                size += Payload.Length;

            // 剩余长度字段
            size += GetVariableByteIntegerLength(size - 1);

            return size;
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // 验证主题名称
            if (!IsTopicValid(Topic))
                return false;

            // 验证QoS级别
            if (!IsQoSLevelValid(QoSLevel))
                return false;

            // 当 QoS > 0 时，必须有数据包标识符
            if (QoSLevel > MqttQoSLevel.AtMostOnce)
            {
                if (!IsPacketIdentifierValid())
                    return false;
            }
            else
            {
                // QoS 0 时不应该有数据包标识符
                if (PacketIdentifier.HasValue)
                    return false;
            }

            // 验证载荷大小
            if (Payload != null && Payload.Length > MqttProtocolConstants.PacketSizeLimits.MaxPacketSize)
                return false;

            return true;
        }

        /// <summary>
        /// 创建 PUBLISH 数据包
        /// </summary>
        /// <param name="topic">主题名称</param>
        /// <param name="payload">消息载荷</param>
        /// <param name="qosLevel">QoS 级别</param>
        /// <param name="retain">保留标志</param>
        /// <param name="packetIdentifier">数据包标识符（当 QoS > 0 时）</param>
        /// <returns>PUBLISH 数据包</returns>
        public static MqttPublishPacket Create(
            string topic,
            byte[] payload,
            MqttQoSLevel qosLevel = MqttQoSLevel.AtMostOnce,
            bool retain = false,
            ushort? packetIdentifier = null)
        {
            var packet = new MqttPublishPacket
            {
                Topic = topic,
                Payload = payload,
                QoSLevel = qosLevel,
                Retain = retain,
                Duplicate = false
            };

            if (qosLevel > MqttQoSLevel.AtMostOnce)
            {
                packet.PacketIdentifier = packetIdentifier ?? throw new ArgumentNullException(nameof(packetIdentifier));
            }

            return packet;
        }

        /// <summary>
        /// 创建重复的 PUBLISH 数据包
        /// </summary>
        /// <returns>标记为重复的 PUBLISH 数据包</returns>
        public MqttPublishPacket CreateDuplicate()
        {
            return new MqttPublishPacket
            {
                Topic = Topic,
                Payload = Payload,
                QoSLevel = QoSLevel,
                Retain = Retain,
                Duplicate = true,
                PacketIdentifier = PacketIdentifier,
                Properties = Properties
            };
        }

        /// <summary>
        /// 从标志位解析 PUBLISH 数据包的标志
        /// </summary>
        /// <param name="flags">标志位字节</param>
        public void ParseFlags(byte flags)
        {
            Duplicate = (flags & 0x08) != 0;
            QoSLevel = (MqttQoSLevel)((flags & 0x06) >> 1);
            Retain = (flags & 0x01) != 0;
            Flags = flags;
        }

        /// <summary>
        /// 检查是否为空消息（用于清除保留消息）
        /// </summary>
        /// <returns>如果是空消息则返回 true，否则返回 false</returns>
        public bool IsEmptyMessage()
        {
            return Payload == null || Payload.Length == 0;
        }

        /// <summary>
        /// 检查是否为保留消息
        /// </summary>
        /// <returns>如果是保留消息则返回 true，否则返回 false</returns>
        public bool IsRetainedMessage()
        {
            return Retain;
        }

        /// <summary>
        /// 获取消息的字符串表示（如果载荷是UTF-8文本）
        /// </summary>
        /// <returns>消息的字符串表示，如果无法解码则返回 null</returns>
        public string? GetPayloadAsString()
        {
            if (Payload == null || Payload.Length == 0)
                return string.Empty;

            try
            {
                return System.Text.Encoding.UTF8.GetString(Payload);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 设置消息载荷为字符串
        /// </summary>
        /// <param name="message">消息字符串</param>
        public void SetPayloadAsString(string message)
        {
            if (string.IsNullOrEmpty(message))
            {
                Payload = Array.Empty<byte>();
            }
            else
            {
                Payload = System.Text.Encoding.UTF8.GetBytes(message);
            }
        }
    }
}
