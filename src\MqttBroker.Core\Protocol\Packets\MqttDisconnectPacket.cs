using System;

namespace MqttBroker.Core.Protocol.Packets
{
    /// <summary>
    /// MQTT DISCONNECT 数据包
    /// </summary>
    public class MqttDisconnectPacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.Disconnect;

        /// <summary>
        /// 原因码 (MQTT 5.0)
        /// </summary>
        public MqttReasonCode ReasonCode { get; set; } = MqttReasonCode.NormalDisconnection;

        /// <summary>
        /// MQTT 5.0 属性（仅适用于 MQTT 5.0）
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            int size = 1; // 固定头部第一个字节

            // MQTT 5.0 原因码和属性
            if (ReasonCode != MqttReasonCode.NormalDisconnection || Properties != null)
            {
                size += 1; // 原因码

                if (Properties != null)
                {
                    size += Properties.GetEstimatedSize();
                }
            }

            // 剩余长度字段
            size += GetVariableByteIntegerLength(size - 1);

            return size;
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // DISCONNECT 不应该有数据包标识符
            if (PacketIdentifier.HasValue)
                return false;

            // DISCONNECT 标志位必须为 0
            if (Flags != 0)
                return false;

            return true;
        }

        /// <summary>
        /// 创建正常断开连接的 DISCONNECT 数据包
        /// </summary>
        /// <returns>DISCONNECT 数据包</returns>
        public static MqttDisconnectPacket CreateNormal()
        {
            return new MqttDisconnectPacket
            {
                ReasonCode = MqttReasonCode.NormalDisconnection
            };
        }

        /// <summary>
        /// 创建带有原因码的 DISCONNECT 数据包
        /// </summary>
        /// <param name="reasonCode">原因码</param>
        /// <param name="properties">属性</param>
        /// <returns>DISCONNECT 数据包</returns>
        public static MqttDisconnectPacket Create(MqttReasonCode reasonCode, MqttProperties? properties = null)
        {
            return new MqttDisconnectPacket
            {
                ReasonCode = reasonCode,
                Properties = properties
            };
        }

        /// <summary>
        /// 创建服务器关闭的 DISCONNECT 数据包
        /// </summary>
        /// <returns>DISCONNECT 数据包</returns>
        public static MqttDisconnectPacket CreateServerShuttingDown()
        {
            return new MqttDisconnectPacket
            {
                ReasonCode = MqttReasonCode.ServerShuttingDown
            };
        }

        /// <summary>
        /// 创建保活超时的 DISCONNECT 数据包
        /// </summary>
        /// <returns>DISCONNECT 数据包</returns>
        public static MqttDisconnectPacket CreateKeepAliveTimeout()
        {
            return new MqttDisconnectPacket
            {
                ReasonCode = MqttReasonCode.KeepAliveTimeout
            };
        }

        /// <summary>
        /// 创建会话被接管的 DISCONNECT 数据包
        /// </summary>
        /// <returns>DISCONNECT 数据包</returns>
        public static MqttDisconnectPacket CreateSessionTakenOver()
        {
            return new MqttDisconnectPacket
            {
                ReasonCode = MqttReasonCode.SessionTakenOver
            };
        }

        /// <summary>
        /// 创建协议错误的 DISCONNECT 数据包
        /// </summary>
        /// <returns>DISCONNECT 数据包</returns>
        public static MqttDisconnectPacket CreateProtocolError()
        {
            return new MqttDisconnectPacket
            {
                ReasonCode = MqttReasonCode.ProtocolError
            };
        }

        /// <summary>
        /// 检查是否为正常断开连接
        /// </summary>
        /// <returns>如果是正常断开连接则返回 true，否则返回 false</returns>
        public bool IsNormalDisconnection()
        {
            return ReasonCode == MqttReasonCode.NormalDisconnection;
        }

        /// <summary>
        /// 检查是否为错误断开连接
        /// </summary>
        /// <returns>如果是错误断开连接则返回 true，否则返回 false</returns>
        public bool IsErrorDisconnection()
        {
            return ReasonCode != MqttReasonCode.NormalDisconnection && 
                   ReasonCode != MqttReasonCode.DisconnectWithWillMessage;
        }
    }

    /// <summary>
    /// MQTT AUTH 数据包 (仅 MQTT 5.0)
    /// </summary>
    public class MqttAuthPacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.Auth;

        /// <summary>
        /// 原因码
        /// </summary>
        public MqttReasonCode ReasonCode { get; set; } = MqttReasonCode.Success;

        /// <summary>
        /// MQTT 5.0 属性
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            int size = 1; // 固定头部第一个字节

            // 原因码
            if (ReasonCode != MqttReasonCode.Success || Properties != null)
            {
                size += 1; // 原因码

                if (Properties != null)
                {
                    size += Properties.GetEstimatedSize();
                }
            }

            // 剩余长度字段
            size += GetVariableByteIntegerLength(size - 1);

            return size;
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // AUTH 不应该有数据包标识符
            if (PacketIdentifier.HasValue)
                return false;

            // AUTH 标志位必须为 0
            if (Flags != 0)
                return false;

            // AUTH 只能用于特定的原因码
            return ReasonCode == MqttReasonCode.Success ||
                   ReasonCode == MqttReasonCode.ContinueAuthentication ||
                   ReasonCode == MqttReasonCode.ReAuthenticate;
        }

        /// <summary>
        /// 创建继续认证的 AUTH 数据包
        /// </summary>
        /// <param name="properties">认证属性</param>
        /// <returns>AUTH 数据包</returns>
        public static MqttAuthPacket CreateContinueAuthentication(MqttProperties? properties = null)
        {
            return new MqttAuthPacket
            {
                ReasonCode = MqttReasonCode.ContinueAuthentication,
                Properties = properties
            };
        }

        /// <summary>
        /// 创建重新认证的 AUTH 数据包
        /// </summary>
        /// <param name="properties">认证属性</param>
        /// <returns>AUTH 数据包</returns>
        public static MqttAuthPacket CreateReAuthenticate(MqttProperties? properties = null)
        {
            return new MqttAuthPacket
            {
                ReasonCode = MqttReasonCode.ReAuthenticate,
                Properties = properties
            };
        }

        /// <summary>
        /// 创建认证成功的 AUTH 数据包
        /// </summary>
        /// <param name="properties">认证属性</param>
        /// <returns>AUTH 数据包</returns>
        public static MqttAuthPacket CreateSuccess(MqttProperties? properties = null)
        {
            return new MqttAuthPacket
            {
                ReasonCode = MqttReasonCode.Success,
                Properties = properties
            };
        }
    }
}
