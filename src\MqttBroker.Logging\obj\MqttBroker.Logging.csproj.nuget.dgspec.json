{"format": 1, "restore": {"D:\\Project\\02 Broker\\src\\MqttBroker.Logging\\MqttBroker.Logging.csproj": {}}, "projects": {"D:\\Project\\02 Broker\\src\\MqttBroker.Logging\\MqttBroker.Logging.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\02 Broker\\src\\MqttBroker.Logging\\MqttBroker.Logging.csproj", "projectName": "MqttBroker.Logging", "projectPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Logging\\MqttBroker.Logging.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\02 Broker\\src\\MqttBroker.Logging\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Serilog.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}