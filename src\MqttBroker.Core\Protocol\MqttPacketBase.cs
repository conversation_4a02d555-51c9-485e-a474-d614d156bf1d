using System;

namespace MqttBroker.Core.Protocol
{
    /// <summary>
    /// MQTT 数据包基类
    /// </summary>
    public abstract class MqttPacketBase : IMqttPacket
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public abstract MqttPacketType PacketType { get; }

        /// <summary>
        /// 数据包标志位
        /// </summary>
        public virtual byte Flags { get; protected set; }

        /// <summary>
        /// 数据包标识符 (如果适用)
        /// </summary>
        public virtual ushort? PacketIdentifier { get; set; }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public abstract int GetEstimatedSize();

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public virtual bool IsValid()
        {
            return true;
        }

        /// <summary>
        /// 获取数据包类型的字节表示
        /// </summary>
        /// <returns>包含数据包类型和标志位的字节</returns>
        protected byte GetPacketTypeByte()
        {
            return (byte)(((byte)PacketType << 4) | (Flags & 0x0F));
        }

        /// <summary>
        /// 验证数据包标识符是否有效
        /// </summary>
        /// <returns>如果数据包标识符有效则返回 true，否则返回 false</returns>
        protected bool IsPacketIdentifierValid()
        {
            // 数据包标识符不能为 0
            return PacketIdentifier.HasValue && PacketIdentifier.Value != 0;
        }

        /// <summary>
        /// 验证主题名称是否有效
        /// </summary>
        /// <param name="topic">主题名称</param>
        /// <returns>如果主题名称有效则返回 true，否则返回 false</returns>
        public static bool IsTopicValid(string? topic)
        {
            if (string.IsNullOrEmpty(topic))
                return false;

            if (topic.Length > MqttProtocolConstants.PacketSizeLimits.MaxTopicLength)
                return false;

            // 主题名称不能包含通配符
            if (topic.Contains(MqttProtocolConstants.TopicWildcards.SingleLevel) ||
                topic.Contains(MqttProtocolConstants.TopicWildcards.MultiLevel))
                return false;

            // 主题名称不能包含空字符
            if (topic.Contains('\0'))
                return false;

            return true;
        }

        /// <summary>
        /// 验证主题过滤器是否有效
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        /// <returns>如果主题过滤器有效则返回 true，否则返回 false</returns>
        public static bool IsTopicFilterValid(string? topicFilter)
        {
            if (string.IsNullOrEmpty(topicFilter))
                return false;

            if (topicFilter.Length > MqttProtocolConstants.PacketSizeLimits.MaxTopicLength)
                return false;

            // 主题过滤器不能包含空字符
            if (topicFilter.Contains('\0'))
                return false;

            // 验证通配符使用规则
            var parts = topicFilter.Split(MqttProtocolConstants.TopicWildcards.Separator);
            
            for (int i = 0; i < parts.Length; i++)
            {
                var part = parts[i];
                
                // 检查单级通配符
                if (part.Contains(MqttProtocolConstants.TopicWildcards.SingleLevel))
                {
                    // 单级通配符必须单独占据一个级别
                    if (part.Length != 1)
                        return false;
                }
                
                // 检查多级通配符
                if (part.Contains(MqttProtocolConstants.TopicWildcards.MultiLevel))
                {
                    // 多级通配符必须单独占据一个级别，且必须是最后一个级别
                    if (part.Length != 1 || i != parts.Length - 1)
                        return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 验证客户端ID是否有效
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>如果客户端ID有效则返回 true，否则返回 false</returns>
        protected static bool IsClientIdValid(string? clientId)
        {
            // 客户端ID可以为空（服务器会自动分配）
            if (string.IsNullOrEmpty(clientId))
                return true;

            if (clientId.Length > MqttProtocolConstants.PacketSizeLimits.MaxClientIdLength)
                return false;

            // 客户端ID不能包含空字符
            if (clientId.Contains('\0'))
                return false;

            return true;
        }

        /// <summary>
        /// 验证QoS级别是否有效
        /// </summary>
        /// <param name="qosLevel">QoS级别</param>
        /// <returns>如果QoS级别有效则返回 true，否则返回 false</returns>
        public static bool IsQoSLevelValid(MqttQoSLevel qosLevel)
        {
            return qosLevel >= MqttQoSLevel.AtMostOnce && qosLevel <= MqttQoSLevel.ExactlyOnce;
        }

        /// <summary>
        /// 验证保活时间是否有效
        /// </summary>
        /// <param name="keepAlive">保活时间（秒）</param>
        /// <returns>如果保活时间有效则返回 true，否则返回 false</returns>
        protected static bool IsKeepAliveValid(ushort keepAlive)
        {
            return keepAlive >= MqttProtocolConstants.KeepAliveLimits.MinKeepAlive &&
                   keepAlive <= MqttProtocolConstants.KeepAliveLimits.MaxKeepAlive;
        }

        /// <summary>
        /// 验证用户名是否有效
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>如果用户名有效则返回 true，否则返回 false</returns>
        protected static bool IsUsernameValid(string? username)
        {
            if (string.IsNullOrEmpty(username))
                return true;

            if (username.Length > MqttProtocolConstants.PacketSizeLimits.MaxUsernameLength)
                return false;

            return true;
        }

        /// <summary>
        /// 验证密码是否有效
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns>如果密码有效则返回 true，否则返回 false</returns>
        protected static bool IsPasswordValid(string? password)
        {
            if (string.IsNullOrEmpty(password))
                return true;

            if (password.Length > MqttProtocolConstants.PacketSizeLimits.MaxPasswordLength)
                return false;

            return true;
        }

        /// <summary>
        /// 计算可变长度整数的编码长度
        /// </summary>
        /// <param name="value">要编码的值</param>
        /// <returns>编码后的字节数</returns>
        public static int GetVariableByteIntegerLength(int value)
        {
            if (value < 0 || value > MqttProtocolConstants.PacketSizeLimits.MaxPacketSize)
                throw new ArgumentOutOfRangeException(nameof(value));

            if (value < 128) return 1;
            if (value < 16384) return 2;
            if (value < 2097152) return 3;
            return 4;
        }

        /// <summary>
        /// 计算UTF-8字符串的编码长度（包括长度前缀）
        /// </summary>
        /// <param name="value">字符串值</param>
        /// <returns>编码后的字节数</returns>
        public static int GetStringLength(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return 2; // 只有长度前缀

            var utf8ByteCount = System.Text.Encoding.UTF8.GetByteCount(value);
            return 2 + utf8ByteCount; // 2字节长度前缀 + UTF-8字节
        }

        /// <summary>
        /// 计算二进制数据的编码长度（包括长度前缀）
        /// </summary>
        /// <param name="data">二进制数据</param>
        /// <returns>编码后的字节数</returns>
        public static int GetBinaryDataLength(byte[]? data)
        {
            if (data == null || data.Length == 0)
                return 2; // 只有长度前缀

            return 2 + data.Length; // 2字节长度前缀 + 数据长度
        }
    }
}
