using System;

namespace MqttBroker.Core.Protocol
{
    /// <summary>
    /// MQTT 数据包类型枚举
    /// </summary>
    public enum MqttPacketType : byte
    {
        /// <summary>
        /// 保留 - 禁止使用
        /// </summary>
        Reserved = 0,

        /// <summary>
        /// 客户端连接请求
        /// </summary>
        Connect = 1,

        /// <summary>
        /// 连接确认
        /// </summary>
        ConnAck = 2,

        /// <summary>
        /// 发布消息
        /// </summary>
        Publish = 3,

        /// <summary>
        /// 发布确认 (QoS 1)
        /// </summary>
        PubAck = 4,

        /// <summary>
        /// 发布收到 (QoS 2 第一步)
        /// </summary>
        PubRec = 5,

        /// <summary>
        /// 发布释放 (QoS 2 第二步)
        /// </summary>
        PubRel = 6,

        /// <summary>
        /// 发布完成 (QoS 2 第三步)
        /// </summary>
        PubComp = 7,

        /// <summary>
        /// 订阅请求
        /// </summary>
        Subscribe = 8,

        /// <summary>
        /// 订阅确认
        /// </summary>
        SubAck = 9,

        /// <summary>
        /// 取消订阅请求
        /// </summary>
        Unsubscribe = 10,

        /// <summary>
        /// 取消订阅确认
        /// </summary>
        UnsubAck = 11,

        /// <summary>
        /// 心跳请求
        /// </summary>
        PingReq = 12,

        /// <summary>
        /// 心跳响应
        /// </summary>
        PingResp = 13,

        /// <summary>
        /// 断开连接
        /// </summary>
        Disconnect = 14,

        /// <summary>
        /// 认证 (仅 MQTT 5.0)
        /// </summary>
        Auth = 15
    }

    /// <summary>
    /// MQTT QoS 级别枚举
    /// </summary>
    public enum MqttQoSLevel : byte
    {
        /// <summary>
        /// QoS 0 - 最多一次传递
        /// </summary>
        AtMostOnce = 0,

        /// <summary>
        /// QoS 1 - 至少一次传递
        /// </summary>
        AtLeastOnce = 1,

        /// <summary>
        /// QoS 2 - 恰好一次传递
        /// </summary>
        ExactlyOnce = 2
    }

    /// <summary>
    /// MQTT 协议版本枚举
    /// </summary>
    public enum MqttProtocolVersion : byte
    {
        /// <summary>
        /// MQTT 3.1.1
        /// </summary>
        Version311 = 4,

        /// <summary>
        /// MQTT 5.0
        /// </summary>
        Version50 = 5
    }

    /// <summary>
    /// MQTT 连接返回码 (MQTT 3.1.1)
    /// </summary>
    public enum MqttConnectReturnCode : byte
    {
        /// <summary>
        /// 连接已接受
        /// </summary>
        ConnectionAccepted = 0,

        /// <summary>
        /// 连接被拒绝，不支持的协议版本
        /// </summary>
        UnacceptableProtocolVersion = 1,

        /// <summary>
        /// 连接被拒绝，标识符被拒绝
        /// </summary>
        IdentifierRejected = 2,

        /// <summary>
        /// 连接被拒绝，服务器不可用
        /// </summary>
        ServerUnavailable = 3,

        /// <summary>
        /// 连接被拒绝，用户名或密码错误
        /// </summary>
        BadUsernameOrPassword = 4,

        /// <summary>
        /// 连接被拒绝，未授权
        /// </summary>
        NotAuthorized = 5
    }

    /// <summary>
    /// MQTT 5.0 原因码
    /// </summary>
    public enum MqttReasonCode : byte
    {
        /// <summary>
        /// 成功
        /// </summary>
        Success = 0x00,

        /// <summary>
        /// 正常断开连接
        /// </summary>
        NormalDisconnection = 0x00,

        /// <summary>
        /// 授权的QoS 0
        /// </summary>
        GrantedQoS0 = 0x00,

        /// <summary>
        /// 授权的QoS 1
        /// </summary>
        GrantedQoS1 = 0x01,

        /// <summary>
        /// 授权的QoS 2
        /// </summary>
        GrantedQoS2 = 0x02,

        /// <summary>
        /// 带有遗嘱消息的断开连接
        /// </summary>
        DisconnectWithWillMessage = 0x04,

        /// <summary>
        /// 无匹配订阅者
        /// </summary>
        NoMatchingSubscribers = 0x10,

        /// <summary>
        /// 订阅不存在
        /// </summary>
        NoSubscriptionExisted = 0x11,

        /// <summary>
        /// 继续认证
        /// </summary>
        ContinueAuthentication = 0x18,

        /// <summary>
        /// 重新认证
        /// </summary>
        ReAuthenticate = 0x19,

        /// <summary>
        /// 未指定错误
        /// </summary>
        UnspecifiedError = 0x80,

        /// <summary>
        /// 格式错误的数据包
        /// </summary>
        MalformedPacket = 0x81,

        /// <summary>
        /// 协议错误
        /// </summary>
        ProtocolError = 0x82,

        /// <summary>
        /// 实现特定错误
        /// </summary>
        ImplementationSpecificError = 0x83,

        /// <summary>
        /// 不支持的协议版本
        /// </summary>
        UnsupportedProtocolVersion = 0x84,

        /// <summary>
        /// 客户端标识符无效
        /// </summary>
        ClientIdentifierNotValid = 0x85,

        /// <summary>
        /// 用户名或密码错误
        /// </summary>
        BadUserNameOrPassword = 0x86,

        /// <summary>
        /// 未授权
        /// </summary>
        NotAuthorized = 0x87,

        /// <summary>
        /// 服务器不可用
        /// </summary>
        ServerUnavailable = 0x88,

        /// <summary>
        /// 服务器繁忙
        /// </summary>
        ServerBusy = 0x89,

        /// <summary>
        /// 被禁止
        /// </summary>
        Banned = 0x8A,

        /// <summary>
        /// 服务器正在关闭
        /// </summary>
        ServerShuttingDown = 0x8B,

        /// <summary>
        /// 认证方法错误
        /// </summary>
        BadAuthenticationMethod = 0x8C,

        /// <summary>
        /// 保活超时
        /// </summary>
        KeepAliveTimeout = 0x8D,

        /// <summary>
        /// 会话被接管
        /// </summary>
        SessionTakenOver = 0x8E,

        /// <summary>
        /// 主题过滤器无效
        /// </summary>
        TopicFilterInvalid = 0x8F,

        /// <summary>
        /// 主题名称无效
        /// </summary>
        TopicNameInvalid = 0x90,

        /// <summary>
        /// 数据包标识符正在使用
        /// </summary>
        PacketIdentifierInUse = 0x91,

        /// <summary>
        /// 数据包标识符未找到
        /// </summary>
        PacketIdentifierNotFound = 0x92,

        /// <summary>
        /// 接收最大值超出
        /// </summary>
        ReceiveMaximumExceeded = 0x93,

        /// <summary>
        /// 主题别名无效
        /// </summary>
        TopicAliasInvalid = 0x94,

        /// <summary>
        /// 数据包过大
        /// </summary>
        PacketTooLarge = 0x95,

        /// <summary>
        /// 消息速率过高
        /// </summary>
        MessageRateTooHigh = 0x96,

        /// <summary>
        /// 配额超出
        /// </summary>
        QuotaExceeded = 0x97,

        /// <summary>
        /// 管理操作
        /// </summary>
        AdministrativeAction = 0x98,

        /// <summary>
        /// 载荷格式无效
        /// </summary>
        PayloadFormatInvalid = 0x99,

        /// <summary>
        /// 不支持保留
        /// </summary>
        RetainNotSupported = 0x9A,

        /// <summary>
        /// 不支持的QoS
        /// </summary>
        QoSNotSupported = 0x9B,

        /// <summary>
        /// 使用另一个服务器
        /// </summary>
        UseAnotherServer = 0x9C,

        /// <summary>
        /// 服务器已移动
        /// </summary>
        ServerMoved = 0x9D,

        /// <summary>
        /// 不支持共享订阅
        /// </summary>
        SharedSubscriptionsNotSupported = 0x9E,

        /// <summary>
        /// 连接速率超出
        /// </summary>
        ConnectionRateExceeded = 0x9F,

        /// <summary>
        /// 最大连接时间
        /// </summary>
        MaximumConnectTime = 0xA0,

        /// <summary>
        /// 不支持订阅标识符
        /// </summary>
        SubscriptionIdentifiersNotSupported = 0xA1,

        /// <summary>
        /// 不支持通配符订阅
        /// </summary>
        WildcardSubscriptionsNotSupported = 0xA2
    }
}
