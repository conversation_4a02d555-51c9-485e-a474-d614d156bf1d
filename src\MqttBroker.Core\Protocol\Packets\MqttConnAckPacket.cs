using System;

namespace MqttBroker.Core.Protocol.Packets
{
    /// <summary>
    /// MQTT CONNACK 数据包
    /// </summary>
    public class MqttConnAckPacket : MqttPacketBase
    {
        /// <summary>
        /// 数据包类型
        /// </summary>
        public override MqttPacketType PacketType => MqttPacketType.ConnAck;

        /// <summary>
        /// 会话存在标志
        /// </summary>
        public bool SessionPresent { get; set; }

        /// <summary>
        /// 连接返回码 (MQTT 3.1.1)
        /// </summary>
        public MqttConnectReturnCode ReturnCode { get; set; }

        /// <summary>
        /// 原因码 (MQTT 5.0)
        /// </summary>
        public MqttReasonCode ReasonCode { get; set; }

        /// <summary>
        /// MQTT 5.0 属性（仅适用于 MQTT 5.0）
        /// </summary>
        public MqttProperties? Properties { get; set; }

        /// <summary>
        /// 获取确认标志字节
        /// </summary>
        public byte AcknowledgeFlags
        {
            get
            {
                return (byte)(SessionPresent ? 0x01 : 0x00);
            }
        }

        /// <summary>
        /// 获取数据包的估计大小
        /// </summary>
        /// <returns>数据包大小（字节）</returns>
        public override int GetEstimatedSize()
        {
            int size = 1; // 固定头部第一个字节

            // 可变头部
            size += 1; // 确认标志
            size += 1; // 返回码/原因码

            // MQTT 5.0 属性
            if (Properties != null)
            {
                size += Properties.GetEstimatedSize();
            }

            // 剩余长度字段
            size += GetVariableByteIntegerLength(size - 1);

            return size;
        }

        /// <summary>
        /// 验证数据包的有效性
        /// </summary>
        /// <returns>如果数据包有效则返回 true，否则返回 false</returns>
        public override bool IsValid()
        {
            // CONNACK 数据包没有数据包标识符
            if (PacketIdentifier.HasValue)
                return false;

            return true;
        }

        /// <summary>
        /// 创建成功的 CONNACK 数据包
        /// </summary>
        /// <param name="sessionPresent">会话是否存在</param>
        /// <param name="protocolVersion">协议版本</param>
        /// <returns>CONNACK 数据包</returns>
        public static MqttConnAckPacket CreateSuccess(bool sessionPresent, MqttProtocolVersion protocolVersion)
        {
            return new MqttConnAckPacket
            {
                SessionPresent = sessionPresent,
                ReturnCode = MqttConnectReturnCode.ConnectionAccepted,
                ReasonCode = MqttReasonCode.Success
            };
        }

        /// <summary>
        /// 创建失败的 CONNACK 数据包 (MQTT 3.1.1)
        /// </summary>
        /// <param name="returnCode">返回码</param>
        /// <returns>CONNACK 数据包</returns>
        public static MqttConnAckPacket CreateFailure(MqttConnectReturnCode returnCode)
        {
            return new MqttConnAckPacket
            {
                SessionPresent = false,
                ReturnCode = returnCode,
                ReasonCode = ConvertReturnCodeToReasonCode(returnCode)
            };
        }

        /// <summary>
        /// 创建失败的 CONNACK 数据包 (MQTT 5.0)
        /// </summary>
        /// <param name="reasonCode">原因码</param>
        /// <param name="properties">属性</param>
        /// <returns>CONNACK 数据包</returns>
        public static MqttConnAckPacket CreateFailure(MqttReasonCode reasonCode, MqttProperties? properties = null)
        {
            return new MqttConnAckPacket
            {
                SessionPresent = false,
                ReturnCode = ConvertReasonCodeToReturnCode(reasonCode),
                ReasonCode = reasonCode,
                Properties = properties
            };
        }

        /// <summary>
        /// 将返回码转换为原因码
        /// </summary>
        /// <param name="returnCode">返回码</param>
        /// <returns>原因码</returns>
        private static MqttReasonCode ConvertReturnCodeToReasonCode(MqttConnectReturnCode returnCode)
        {
            return returnCode switch
            {
                MqttConnectReturnCode.ConnectionAccepted => MqttReasonCode.Success,
                MqttConnectReturnCode.UnacceptableProtocolVersion => MqttReasonCode.UnsupportedProtocolVersion,
                MqttConnectReturnCode.IdentifierRejected => MqttReasonCode.ClientIdentifierNotValid,
                MqttConnectReturnCode.ServerUnavailable => MqttReasonCode.ServerUnavailable,
                MqttConnectReturnCode.BadUsernameOrPassword => MqttReasonCode.BadUserNameOrPassword,
                MqttConnectReturnCode.NotAuthorized => MqttReasonCode.NotAuthorized,
                _ => MqttReasonCode.UnspecifiedError
            };
        }

        /// <summary>
        /// 将原因码转换为返回码
        /// </summary>
        /// <param name="reasonCode">原因码</param>
        /// <returns>返回码</returns>
        private static MqttConnectReturnCode ConvertReasonCodeToReturnCode(MqttReasonCode reasonCode)
        {
            return reasonCode switch
            {
                MqttReasonCode.Success => MqttConnectReturnCode.ConnectionAccepted,
                MqttReasonCode.UnsupportedProtocolVersion => MqttConnectReturnCode.UnacceptableProtocolVersion,
                MqttReasonCode.ClientIdentifierNotValid => MqttConnectReturnCode.IdentifierRejected,
                MqttReasonCode.ServerUnavailable => MqttConnectReturnCode.ServerUnavailable,
                MqttReasonCode.BadUserNameOrPassword => MqttConnectReturnCode.BadUsernameOrPassword,
                MqttReasonCode.NotAuthorized => MqttConnectReturnCode.NotAuthorized,
                _ => MqttConnectReturnCode.ServerUnavailable
            };
        }
    }
}
